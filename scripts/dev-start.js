#!/usr/bin/env node

/**
 * FastTransfer Development Startup Script
 * 
 * This script launches all services needed for local development:
 * - Frontend (Vite dev server on port 5173)
 * - Backend API (Express server on port 3000)
 * - Worker Service (ZMT compression service on port 3001)
 * 
 * Usage:
 *   npm run dev:all
 *   node scripts/dev-start.js
 *   ./scripts/dev-start.js
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const config = {
  services: [
    {
      name: 'Frontend',
      command: 'npm',
      args: ['run', 'dev'],
      cwd: path.join(__dirname, '../packages/frontend'),
      port: 5173,
      color: '\x1b[36m', // Cyan
      readyPattern: /Local:\s+http:\/\/localhost:5173/,
      env: { ...process.env }
    },
    {
      name: 'Backend',
      command: 'npm',
      args: ['run', 'dev'],
      cwd: path.join(__dirname, '../services/backend'),
      port: 3000,
      color: '\x1b[32m', // Green
      readyPattern: /Server running on port 3000|listening on port 3000|Server started on port 3000/,
      env: {
        ...process.env,
        NODE_ENV: 'development',
        PORT: '3000'
      }
    }
    // Worker service disabled due to compilation issues
    // To enable worker service, fix the TypeScript errors in services/worker/src/worker.ts
    // and uncomment the configuration below:
    // {
    //   name: 'Worker',
    //   command: 'npm',
    //   args: ['run', 'dev'],
    //   cwd: path.join(__dirname, '../services/worker'),
    //   port: 3001,
    //   color: '\x1b[33m', // Yellow
    //   readyPattern: /Worker service listening on port 3001|ZMT worker started/,
    //   env: {
    //     ...process.env,
    //     NODE_ENV: 'development',
    //     PORT: '3001'
    //   }
    // }
  ],
  colors: {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
  }
};

// Global state
const processes = new Map();
const readyServices = new Set();
let shutdownInProgress = false;

/**
 * Print colored log message
 */
function log(service, message, isError = false) {
  const timestamp = new Date().toLocaleTimeString();
  const color = service ? config.services.find(s => s.name === service)?.color || config.colors.white : config.colors.white;
  const prefix = service ? `[${service}]` : '[MAIN]';
  const logColor = isError ? config.colors.red : color;
  
  console.log(`${config.colors.bright}${timestamp}${config.colors.reset} ${logColor}${prefix}${config.colors.reset} ${message}`);
}

/**
 * Check if all required directories exist
 */
function validateDirectories() {
  const missingDirs = [];
  
  for (const service of config.services) {
    if (!fs.existsSync(service.cwd)) {
      missingDirs.push(`${service.name}: ${service.cwd}`);
    }
    
    const packageJsonPath = path.join(service.cwd, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      missingDirs.push(`${service.name} package.json: ${packageJsonPath}`);
    }
  }
  
  if (missingDirs.length > 0) {
    log(null, `${config.colors.red}Missing required directories/files:${config.colors.reset}`, true);
    missingDirs.forEach(dir => log(null, `  - ${dir}`, true));
    process.exit(1);
  }
}

/**
 * Check if ports are available
 */
async function checkPorts() {
  const net = require('net');
  
  for (const service of config.services) {
    try {
      await new Promise((resolve, reject) => {
        const server = net.createServer();
        server.listen(service.port, () => {
          server.close(resolve);
        });
        server.on('error', reject);
      });
    } catch (error) {
      if (error.code === 'EADDRINUSE') {
        log(null, `${config.colors.yellow}Warning: Port ${service.port} is already in use (${service.name})${config.colors.reset}`);
        log(null, `This might cause conflicts. Consider stopping other services on this port.`);
      }
    }
  }
}

/**
 * Start a service
 */
function startService(service) {
  log(service.name, `Starting ${service.name.toLowerCase()} service...`);
  log(service.name, `Command: ${service.command} ${service.args.join(' ')}`);
  log(service.name, `Working directory: ${service.cwd}`);
  
  const child = spawn(service.command, service.args, {
    cwd: service.cwd,
    env: service.env,
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  processes.set(service.name, child);
  
  // Handle stdout
  child.stdout.on('data', (data) => {
    const output = data.toString().trim();
    if (output) {
      // Check if service is ready
      if (service.readyPattern && service.readyPattern.test(output)) {
        readyServices.add(service.name);
        log(service.name, `${config.colors.green}✓ Service ready!${config.colors.reset}`);
        checkAllServicesReady();
      }
      
      // Log output with service prefix
      output.split('\n').forEach(line => {
        if (line.trim()) {
          log(service.name, line);
        }
      });
    }
  });
  
  // Handle stderr
  child.stderr.on('data', (data) => {
    const output = data.toString().trim();
    if (output) {
      output.split('\n').forEach(line => {
        if (line.trim()) {
          log(service.name, line, true);
        }
      });
    }
  });
  
  // Handle process exit
  child.on('exit', (code, signal) => {
    processes.delete(service.name);
    readyServices.delete(service.name);
    
    if (!shutdownInProgress) {
      if (code === 0) {
        log(service.name, `${config.colors.yellow}Service exited normally${config.colors.reset}`);
      } else {
        log(service.name, `${config.colors.red}Service exited with code ${code}${signal ? ` (${signal})` : ''}${config.colors.reset}`, true);
      }
    }
  });
  
  // Handle process errors
  child.on('error', (error) => {
    log(service.name, `${config.colors.red}Failed to start: ${error.message}${config.colors.reset}`, true);
  });
}

/**
 * Check if all services are ready
 */
function checkAllServicesReady() {
  if (readyServices.size === config.services.length) {
    log(null, `${config.colors.green}${config.colors.bright}🚀 All services are ready!${config.colors.reset}`);
    log(null, '');
    log(null, `${config.colors.bright}Available services:${config.colors.reset}`);
    log(null, `  ${config.colors.cyan}Frontend:${config.colors.reset} http://localhost:5173`);
    log(null, `  ${config.colors.green}Backend API:${config.colors.reset} http://localhost:3000`);
    log(null, `  ${config.colors.yellow}Worker Service:${config.colors.reset} Disabled (fix TypeScript errors to enable)`);
    log(null, '');
    log(null, `${config.colors.bright}Press Ctrl+C to stop all services${config.colors.reset}`);
  }
}

/**
 * Graceful shutdown
 */
function shutdown() {
  if (shutdownInProgress) return;
  shutdownInProgress = true;
  
  log(null, `${config.colors.yellow}Shutting down all services...${config.colors.reset}`);
  
  const shutdownPromises = Array.from(processes.entries()).map(([name, process]) => {
    return new Promise((resolve) => {
      log(name, 'Stopping...');
      
      // Try graceful shutdown first
      process.kill('SIGTERM');
      
      // Force kill after timeout
      const timeout = setTimeout(() => {
        log(name, 'Force killing...');
        process.kill('SIGKILL');
      }, 5000);
      
      process.on('exit', () => {
        clearTimeout(timeout);
        log(name, 'Stopped');
        resolve();
      });
    });
  });
  
  Promise.all(shutdownPromises).then(() => {
    log(null, `${config.colors.green}All services stopped${config.colors.reset}`);
    process.exit(0);
  });
}

/**
 * Main startup function
 */
async function main() {
  // Print banner
  console.log(`${config.colors.bright}${config.colors.blue}`);
  console.log('╔══════════════════════════════════════════════════════════════╗');
  console.log('║                    FastTransfer Development                  ║');
  console.log('║                      Service Launcher                       ║');
  console.log('╚══════════════════════════════════════════════════════════════╝');
  console.log(`${config.colors.reset}`);
  
  // Validate environment
  log(null, 'Validating environment...');
  validateDirectories();
  
  // Check ports
  log(null, 'Checking port availability...');
  await checkPorts();
  
  // Install dependencies if needed
  log(null, 'Checking dependencies...');
  for (const service of config.services) {
    const nodeModulesPath = path.join(service.cwd, 'node_modules');
    if (!fs.existsSync(nodeModulesPath)) {
      log(service.name, `${config.colors.yellow}Installing dependencies...${config.colors.reset}`);
      // Note: In production, you might want to run npm install here
      log(service.name, `${config.colors.yellow}Please run 'npm install' in ${service.cwd}${config.colors.reset}`);
    }
  }
  
  // Setup signal handlers
  process.on('SIGINT', shutdown);
  process.on('SIGTERM', shutdown);
  process.on('SIGQUIT', shutdown);
  
  // Start all services
  log(null, `Starting ${config.services.length} services...`);
  config.services.forEach(startService);
  
  // Keep the process alive
  process.stdin.resume();
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  log(null, `${config.colors.red}Uncaught exception: ${error.message}${config.colors.reset}`, true);
  console.error(error.stack);
  shutdown();
});

process.on('unhandledRejection', (reason, promise) => {
  log(null, `${config.colors.red}Unhandled rejection at: ${promise}, reason: ${reason}${config.colors.reset}`, true);
  shutdown();
});

// Run if this script is executed directly
if (require.main === module) {
  main().catch(error => {
    log(null, `${config.colors.red}Startup failed: ${error.message}${config.colors.reset}`, true);
    console.error(error.stack);
    process.exit(1);
  });
}

module.exports = { main, shutdown };
