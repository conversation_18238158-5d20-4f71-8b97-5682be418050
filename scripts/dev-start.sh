#!/bin/bash

# FastTransfer Development Startup Script (Shell Version)
# 
# This script launches all services needed for local development:
# - Frontend (Vite dev server on port 5173)
# - Backend API (Express server on port 3000)
# - Worker Service (ZMT compression service on port 3001)
# 
# Usage:
#   ./scripts/dev-start.sh
#   bash scripts/dev-start.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FRONTEND_DIR="$PROJECT_ROOT/packages/frontend"
BACKEND_DIR="$PROJECT_ROOT/services/backend"
WORKER_DIR="$PROJECT_ROOT/services/worker"

# PID tracking
PIDS=()
SERVICES=("Frontend" "Backend" "Worker")

# Print colored log message
log() {
    local service="$1"
    local message="$2"
    local color="$3"
    local timestamp=$(date '+%H:%M:%S')
    
    if [ -n "$color" ]; then
        echo -e "${BOLD}${timestamp}${NC} ${color}[${service}]${NC} ${message}"
    else
        echo -e "${BOLD}${timestamp}${NC} [${service}] ${message}"
    fi
}

# Print banner
print_banner() {
    echo -e "${BOLD}${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    FastTransfer Development                  ║"
    echo "║                      Service Launcher                       ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Check if directory exists
check_directory() {
    local dir="$1"
    local name="$2"
    
    if [ ! -d "$dir" ]; then
        log "MAIN" "Directory not found: $dir" "$RED"
        log "MAIN" "Please ensure $name is properly set up" "$RED"
        exit 1
    fi
    
    if [ ! -f "$dir/package.json" ]; then
        log "MAIN" "package.json not found in: $dir" "$RED"
        log "MAIN" "Please ensure $name is properly configured" "$RED"
        exit 1
    fi
}

# Check if port is available
check_port() {
    local port="$1"
    local service="$2"
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log "MAIN" "Warning: Port $port is already in use ($service)" "$YELLOW"
        log "MAIN" "This might cause conflicts. Consider stopping other services on this port." "$YELLOW"
    fi
}

# Install dependencies if needed
check_dependencies() {
    local dir="$1"
    local name="$2"
    
    if [ ! -d "$dir/node_modules" ]; then
        log "$name" "Installing dependencies..." "$YELLOW"
        cd "$dir"
        npm install
        cd "$PROJECT_ROOT"
    fi
}

# Cleanup function
cleanup() {
    log "MAIN" "Shutting down all services..." "$YELLOW"
    
    # Kill all background processes
    for pid in "${PIDS[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            log "MAIN" "Stopping process $pid..." "$YELLOW"
            kill -TERM "$pid" 2>/dev/null || true
        fi
    done
    
    # Wait a bit for graceful shutdown
    sleep 2
    
    # Force kill if still running
    for pid in "${PIDS[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            log "MAIN" "Force killing process $pid..." "$RED"
            kill -KILL "$pid" 2>/dev/null || true
        fi
    done
    
    log "MAIN" "All services stopped" "$GREEN"
    exit 0
}

# Start a service in background
start_service() {
    local name="$1"
    local dir="$2"
    local command="$3"
    local color="$4"
    
    log "$name" "Starting $name service..." "$color"
    log "$name" "Working directory: $dir" "$color"
    log "$name" "Command: $command" "$color"
    
    cd "$dir"
    
    # Start the service in background and capture PID
    eval "$command" 2>&1 | while IFS= read -r line; do
        log "$name" "$line" "$color"
    done &
    
    local pid=$!
    PIDS+=($pid)
    
    cd "$PROJECT_ROOT"
    
    log "$name" "Started with PID: $pid" "$color"
}

# Wait for services to be ready
wait_for_services() {
    log "MAIN" "Waiting for services to be ready..." "$BLUE"
    
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        local ready_count=0
        
        # Check frontend (port 5173)
        if curl -s http://localhost:5173 >/dev/null 2>&1; then
            ((ready_count++))
        fi
        
        # Check backend (port 3000)
        if curl -s http://localhost:3000/api/health >/dev/null 2>&1 || curl -s http://localhost:3000 >/dev/null 2>&1; then
            ((ready_count++))
        fi
        
        # Check worker (port 3001)
        if curl -s http://localhost:3001/health >/dev/null 2>&1 || curl -s http://localhost:3001 >/dev/null 2>&1; then
            ((ready_count++))
        fi
        
        if [ $ready_count -eq 3 ]; then
            log "MAIN" "🚀 All services are ready!" "$GREEN"
            echo ""
            log "MAIN" "Available services:" "$BOLD"
            log "MAIN" "  Frontend: http://localhost:5173" "$CYAN"
            log "MAIN" "  Backend API: http://localhost:3000" "$GREEN"
            log "MAIN" "  Worker Service: http://localhost:3001" "$YELLOW"
            echo ""
            log "MAIN" "Press Ctrl+C to stop all services" "$BOLD"
            return 0
        fi
        
        ((attempt++))
        sleep 2
    done
    
    log "MAIN" "Some services may not be fully ready yet, but continuing..." "$YELLOW"
}

# Main function
main() {
    # Print banner
    print_banner
    
    # Validate environment
    log "MAIN" "Validating environment..." "$BLUE"
    check_directory "$FRONTEND_DIR" "Frontend"
    check_directory "$BACKEND_DIR" "Backend"
    check_directory "$WORKER_DIR" "Worker"
    
    # Check ports
    log "MAIN" "Checking port availability..." "$BLUE"
    check_port 5173 "Frontend"
    check_port 3000 "Backend"
    check_port 3001 "Worker"
    
    # Check dependencies
    log "MAIN" "Checking dependencies..." "$BLUE"
    check_dependencies "$FRONTEND_DIR" "Frontend"
    check_dependencies "$BACKEND_DIR" "Backend"
    check_dependencies "$WORKER_DIR" "Worker"
    
    # Setup signal handlers
    trap cleanup SIGINT SIGTERM SIGQUIT
    
    # Start services
    log "MAIN" "Starting all services..." "$BLUE"
    
    # Start Frontend (Vite dev server)
    start_service "Frontend" "$FRONTEND_DIR" "npm run dev" "$CYAN"
    sleep 2
    
    # Start Backend (Express server)
    start_service "Backend" "$BACKEND_DIR" "npm run dev" "$GREEN"
    sleep 2
    
    # Start Worker (ZMT compression service)
    start_service "Worker" "$WORKER_DIR" "npm run dev" "$YELLOW"
    sleep 3
    
    # Wait for services to be ready
    wait_for_services
    
    # Keep script running
    log "MAIN" "All services started. Monitoring..." "$GREEN"
    
    # Wait for all background processes
    wait
}

# Check if required commands are available
command -v node >/dev/null 2>&1 || { log "MAIN" "Node.js is required but not installed. Aborting." "$RED"; exit 1; }
command -v npm >/dev/null 2>&1 || { log "MAIN" "npm is required but not installed. Aborting." "$RED"; exit 1; }

# Run main function
main "$@"
