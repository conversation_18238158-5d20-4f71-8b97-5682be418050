# FastTransfer Frontend Testing Guide

## Overview
This guide provides comprehensive testing instructions for the FastTransfer web application frontend.

## Prerequisites
- Backend server running on http://localhost:3000
- Frontend development server running on http://localhost:5173
- Test files available for upload

## Test Scenarios

### 1. Drag and Drop Upload Interface ✅
**Objective**: Verify drag and drop functionality works correctly

**Steps**:
1. Open http://localhost:5173 in browser
2. Drag a file from your file system onto the upload area
3. Verify the upload area highlights when dragging over it
4. Drop the file and verify upload starts
5. Test with multiple files simultaneously
6. Test with different file types (txt, pdf, images, etc.)

**Expected Results**:
- Upload area should highlight with blue border when dragging
- Files should appear in the transfer list immediately
- Upload status should show "Uploading..." with animated icon

### 2. File Compression Simulation ✅
**Objective**: Verify compression status updates and visual indicators

**Steps**:
1. Upload a file using drag and drop
2. Observe the status progression:
   - "Uploading..." (blue icon, animated)
   - "Compressing..." (yellow icon, animated, progress bar)
   - "Ready" (green checkmark icon)
3. Verify compression ratio is displayed
4. Check that file size reduction percentage is shown

**Expected Results**:
- Status icons should animate during processing
- Progress bar should appear during compression
- Compression ratio should be displayed (e.g., "45% smaller")
- Visual feedback should be clear and intuitive

### 3. Transfer Status Polling ✅
**Objective**: Verify real-time status updates

**Steps**:
1. Upload a file and monitor status changes
2. Verify polling happens automatically every 2 seconds
3. Check browser network tab for API calls
4. Verify status updates without page refresh

**Expected Results**:
- Status should update automatically
- No manual refresh required
- Network requests should show polling to /api/transfer/{id}/status

### 4. Download Functionality ✅
**Objective**: Test file download after compression

**Steps**:
1. Upload a file and wait for "Ready" status
2. Click the "Download" button
3. Verify file downloads with .zmt extension
4. Check downloaded file content matches original

**Expected Results**:
- Download button should only appear when status is "Ready"
- File should download immediately when clicked
- Downloaded filename should include .zmt extension
- File content should be preserved

### 5. Error Handling
**Objective**: Verify graceful error handling

**Steps**:
1. Stop the backend server temporarily
2. Try uploading a file
3. Restart backend and test again
4. Test with very large files (if applicable)
5. Test with invalid file types (if restrictions exist)

**Expected Results**:
- Error status should be displayed clearly
- User should receive meaningful error messages
- Application should not crash or become unresponsive

### 6. UI Responsiveness
**Objective**: Ensure interface works on different screen sizes

**Steps**:
1. Test on desktop browser (full screen)
2. Resize browser window to tablet size
3. Resize to mobile size
4. Test all functionality at different sizes

**Expected Results**:
- Layout should adapt to different screen sizes
- All buttons and text should remain readable
- Drag and drop should work on touch devices
- No horizontal scrolling required

## Manual Testing Checklist

- [ ] Drag and drop single file
- [ ] Drag and drop multiple files
- [ ] Click to select files
- [ ] Upload progress indication
- [ ] Compression status updates
- [ ] Download functionality
- [ ] Error state handling
- [ ] Responsive design
- [ ] Browser console (no errors)
- [ ] Network requests (proper API calls)

## Test Files
Use the provided `test-file.txt` or create your own test files of various sizes and types.

## Browser Console
Monitor the browser console for any JavaScript errors or warnings during testing.

## Performance Notes
- File uploads should start immediately
- Status updates should be responsive (2-second polling)
- UI should remain interactive during uploads
- No memory leaks during extended use

## Success Criteria
All test scenarios should pass without errors, and the user experience should be smooth and intuitive.
