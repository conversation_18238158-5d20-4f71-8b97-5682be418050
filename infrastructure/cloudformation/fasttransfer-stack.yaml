AWSTemplateFormatVersion: '2010-09-09'
Description: 'FastTransfer - High-speed file compression and transfer platform'

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment name
  
  InstanceType:
    Type: String
    Default: t3.medium
    Description: EC2 instance type for worker nodes
  
  KeyPairName:
    Type: AWS::EC2::KeyPair::KeyName
    Description: EC2 Key Pair for SSH access

Resources:
  # S3 Bucket for file storage
  FileStorageBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub 'fasttransfer-files-${Environment}-${AWS::AccountId}'
      VersioningConfiguration:
        Status: Enabled
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldVersions
            Status: Enabled
            NoncurrentVersionExpirationInDays: 7
          - Id: DeleteIncompleteUploads
            Status: Enabled
            AbortIncompleteMultipartUpload:
              DaysAfterInitiation: 1
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders: ['*']
            AllowedMethods: [GET, PUT, POST, DELETE, HEAD]
            AllowedOrigins: ['*']
            MaxAge: 3000
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true

  # CloudFront Distribution for CDN
  CloudFrontDistribution:
    Type: AWS::CloudFront::Distribution
    Properties:
      DistributionConfig:
        Origins:
          - Id: S3Origin
            DomainName: !GetAtt FileStorageBucket.RegionalDomainName
            S3OriginConfig:
              OriginAccessIdentity: !Sub 'origin-access-identity/cloudfront/${CloudFrontOAI}'
        Enabled: true
        DefaultCacheBehavior:
          TargetOriginId: S3Origin
          ViewerProtocolPolicy: redirect-to-https
          CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad  # CachingDisabled
          OriginRequestPolicyId: 88a5eaf4-2fd4-4709-b370-b4c650ea3fcf  # CORS-S3Origin
        PriceClass: PriceClass_100
        ViewerCertificate:
          CloudFrontDefaultCertificate: true

  # CloudFront Origin Access Identity
  CloudFrontOAI:
    Type: AWS::CloudFront::OriginAccessIdentity
    Properties:
      OriginAccessIdentityConfig:
        Comment: !Sub 'OAI for FastTransfer ${Environment}'

  # S3 Bucket Policy for CloudFront
  BucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref FileStorageBucket
      PolicyDocument:
        Statement:
          - Sid: AllowCloudFrontAccess
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${CloudFrontOAI}'
            Action: 's3:GetObject'
            Resource: !Sub '${FileStorageBucket}/*'

  # DynamoDB Table for user accounts
  UserTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub 'fasttransfer-users-${Environment}'
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: email
          AttributeType: S
        - AttributeName: status
          AttributeType: S
        - AttributeName: createdAt
          AttributeType: N
      KeySchema:
        - AttributeName: email
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: StatusIndex
          KeySchema:
            - AttributeName: status
              KeyType: HASH
            - AttributeName: createdAt
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true

  # DynamoDB Table for transfer metadata
  TransferTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub 'fasttransfer-transfers-${Environment}'
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: transferId
          AttributeType: S
        - AttributeName: userId
          AttributeType: S
        - AttributeName: createdAt
          AttributeType: S
      KeySchema:
        - AttributeName: transferId
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: UserTransfersIndex
          KeySchema:
            - AttributeName: userId
              KeyType: HASH
            - AttributeName: createdAt
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true

  # SQS Queue for compression jobs
  CompressionQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub 'fasttransfer-compression-${Environment}'
      VisibilityTimeoutSeconds: 300
      MessageRetentionPeriod: 1209600  # 14 days
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt CompressionDLQ.Arn
        maxReceiveCount: 3

  # Dead Letter Queue for failed compression jobs
  CompressionDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub 'fasttransfer-compression-dlq-${Environment}'
      MessageRetentionPeriod: 1209600  # 14 days

  # SQS Queue for decompression jobs
  DecompressionQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub 'fasttransfer-decompression-${Environment}'
      VisibilityTimeoutSeconds: 300
      MessageRetentionPeriod: 1209600  # 14 days
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt DecompressionDLQ.Arn
        maxReceiveCount: 3

  # Dead Letter Queue for failed decompression jobs
  DecompressionDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub 'fasttransfer-decompression-dlq-${Environment}'
      MessageRetentionPeriod: 1209600  # 14 days

  # VPC for EC2 instances
  VPC:
    Type: AWS::EC2::VPC
    Properties:
      CidrBlock: 10.0.0.0/16
      EnableDnsHostnames: true
      EnableDnsSupport: true
      Tags:
        - Key: Name
          Value: !Sub 'fasttransfer-vpc-${Environment}'

  # Internet Gateway
  InternetGateway:
    Type: AWS::EC2::InternetGateway
    Properties:
      Tags:
        - Key: Name
          Value: !Sub 'fasttransfer-igw-${Environment}'

  # Attach Internet Gateway to VPC
  InternetGatewayAttachment:
    Type: AWS::EC2::VPCGatewayAttachment
    Properties:
      InternetGatewayId: !Ref InternetGateway
      VpcId: !Ref VPC

  # Public Subnet
  PublicSubnet:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Select [0, !GetAZs '']
      CidrBlock: ********/24
      MapPublicIpOnLaunch: true
      Tags:
        - Key: Name
          Value: !Sub 'fasttransfer-public-subnet-${Environment}'

  # Route Table for Public Subnet
  PublicRouteTable:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: Name
          Value: !Sub 'fasttransfer-public-rt-${Environment}'

  # Default Route for Public Subnet
  DefaultPublicRoute:
    Type: AWS::EC2::Route
    DependsOn: InternetGatewayAttachment
    Properties:
      RouteTableId: !Ref PublicRouteTable
      DestinationCidrBlock: 0.0.0.0/0
      GatewayId: !Ref InternetGateway

  # Associate Public Subnet with Route Table
  PublicSubnetRouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref PublicRouteTable
      SubnetId: !Ref PublicSubnet

  # Security Group for EC2 instances
  EC2SecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub 'fasttransfer-ec2-sg-${Environment}'
      GroupDescription: Security group for FastTransfer EC2 instances
      VpcId: !Ref VPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: 0.0.0.0/0
          Description: SSH access
        - IpProtocol: tcp
          FromPort: 3000
          ToPort: 3000
          CidrIp: 0.0.0.0/0
          Description: Backend API
        - IpProtocol: tcp
          FromPort: 3001
          ToPort: 3001
          CidrIp: 0.0.0.0/0
          Description: Worker service
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          Description: All outbound traffic
      Tags:
        - Key: Name
          Value: !Sub 'fasttransfer-ec2-sg-${Environment}'

  # IAM Role for EC2 instances
  EC2Role:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub 'fasttransfer-ec2-role-${Environment}'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy
      Policies:
        - PolicyName: FastTransferS3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:ListBucket
                Resource:
                  - !Sub '${FileStorageBucket}/*'
                  - !GetAtt FileStorageBucket.Arn
        - PolicyName: FastTransferSQSAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - sqs:ReceiveMessage
                  - sqs:DeleteMessage
                  - sqs:SendMessage
                  - sqs:GetQueueAttributes
                Resource:
                  - !GetAtt CompressionQueue.Arn
                  - !GetAtt DecompressionQueue.Arn
        - PolicyName: FastTransferDynamoDBAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:GetItem
                  - dynamodb:PutItem
                  - dynamodb:UpdateItem
                  - dynamodb:DeleteItem
                  - dynamodb:Query
                  - dynamodb:Scan
                Resource:
                  - !GetAtt TransferTable.Arn
                  - !Sub '${TransferTable.Arn}/index/*'

  # Instance Profile for EC2 Role
  EC2InstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      InstanceProfileName: !Sub 'fasttransfer-ec2-profile-${Environment}'
      Roles:
        - !Ref EC2Role

  # Launch Template for EC2 instances
  EC2LaunchTemplate:
    Type: AWS::EC2::LaunchTemplate
    Properties:
      LaunchTemplateName: !Sub 'fasttransfer-lt-${Environment}'
      LaunchTemplateData:
        ImageId: ami-0c02fb55956c7d316  # Amazon Linux 2023 AMI (update as needed)
        InstanceType: !Ref InstanceType
        KeyName: !Ref KeyPairName
        IamInstanceProfile:
          Arn: !GetAtt EC2InstanceProfile.Arn
        SecurityGroupIds:
          - !Ref EC2SecurityGroup
        UserData:
          Fn::Base64: !Sub |
            #!/bin/bash
            yum update -y
            yum install -y docker git
            systemctl start docker
            systemctl enable docker
            usermod -a -G docker ec2-user

            # Install Node.js 18
            curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
            yum install -y nodejs

            # Install PM2 for process management
            npm install -g pm2

            # Clone the application (replace with your repo)
            cd /home/<USER>
            # git clone https://github.com/your-repo/fast-transfer.git
            # cd fast-transfer

            # Set environment variables
            echo "AWS_REGION=${AWS::Region}" >> /etc/environment
            echo "S3_BUCKET=${FileStorageBucket}" >> /etc/environment
            echo "COMPRESSION_QUEUE_URL=${CompressionQueue}" >> /etc/environment
            echo "DECOMPRESSION_QUEUE_URL=${DecompressionQueue}" >> /etc/environment
            echo "DYNAMODB_TABLE=${TransferTable}" >> /etc/environment

            # Install and configure CloudWatch agent
            wget https://s3.amazonaws.com/amazoncloudwatch-agent/amazon_linux/amd64/latest/amazon-cloudwatch-agent.rpm
            rpm -U ./amazon-cloudwatch-agent.rpm

  # Backend EC2 Instance
  BackendInstance:
    Type: AWS::EC2::Instance
    Properties:
      LaunchTemplate:
        LaunchTemplateId: !Ref EC2LaunchTemplate
        Version: !GetAtt EC2LaunchTemplate.LatestVersionNumber
      SubnetId: !Ref PublicSubnet
      Tags:
        - Key: Name
          Value: !Sub 'fasttransfer-backend-${Environment}'
        - Key: Role
          Value: backend

  # Worker EC2 Instance
  WorkerInstance:
    Type: AWS::EC2::Instance
    Properties:
      LaunchTemplate:
        LaunchTemplateId: !Ref EC2LaunchTemplate
        Version: !GetAtt EC2LaunchTemplate.LatestVersionNumber
      SubnetId: !Ref PublicSubnet
      Tags:
        - Key: Name
          Value: !Sub 'fasttransfer-worker-${Environment}'
        - Key: Role
          Value: worker

Outputs:
  S3BucketName:
    Description: Name of the S3 bucket for file storage
    Value: !Ref FileStorageBucket
    Export:
      Name: !Sub '${AWS::StackName}-S3Bucket'

  CloudFrontDistributionId:
    Description: CloudFront Distribution ID
    Value: !Ref CloudFrontDistribution
    Export:
      Name: !Sub '${AWS::StackName}-CloudFrontDistribution'

  CloudFrontDomainName:
    Description: CloudFront Distribution Domain Name
    Value: !GetAtt CloudFrontDistribution.DomainName
    Export:
      Name: !Sub '${AWS::StackName}-CloudFrontDomain'

  DynamoDBTableName:
    Description: Name of the DynamoDB table for transfer metadata
    Value: !Ref TransferTable
    Export:
      Name: !Sub '${AWS::StackName}-DynamoDBTable'

  CompressionQueueUrl:
    Description: URL of the compression SQS queue
    Value: !Ref CompressionQueue
    Export:
      Name: !Sub '${AWS::StackName}-CompressionQueue'

  DecompressionQueueUrl:
    Description: URL of the decompression SQS queue
    Value: !Ref DecompressionQueue
    Export:
      Name: !Sub '${AWS::StackName}-DecompressionQueue'

  BackendInstanceId:
    Description: Instance ID of the backend server
    Value: !Ref BackendInstance
    Export:
      Name: !Sub '${AWS::StackName}-BackendInstance'

  BackendPublicIP:
    Description: Public IP address of the backend server
    Value: !GetAtt BackendInstance.PublicIp
    Export:
      Name: !Sub '${AWS::StackName}-BackendPublicIP'

  WorkerInstanceId:
    Description: Instance ID of the worker server
    Value: !Ref WorkerInstance
    Export:
      Name: !Sub '${AWS::StackName}-WorkerInstance'

  WorkerPublicIP:
    Description: Public IP address of the worker server
    Value: !GetAtt WorkerInstance.PublicIp
    Export:
      Name: !Sub '${AWS::StackName}-WorkerPublicIP'

  VPCId:
    Description: ID of the VPC
    Value: !Ref VPC
    Export:
      Name: !Sub '${AWS::StackName}-VPC'
