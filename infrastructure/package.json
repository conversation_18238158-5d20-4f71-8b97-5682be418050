{"name": "fast-transfer-infrastructure", "version": "1.0.0", "description": "AWS CDK infrastructure for FastTransfer", "main": "lib/index.js", "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "deploy": "npm run build && cdk deploy --all", "destroy": "cdk destroy --all", "diff": "cdk diff", "synth": "cdk synth"}, "dependencies": {"aws-cdk-lib": "^2.100.0", "constructs": "^10.3.0"}, "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "^20.6.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2", "aws-cdk": "^2.100.0"}}