#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { FastTransferStack } from '../lib/fast-transfer-stack';

const app = new cdk.App();

new FastTransferStack(app, 'FastTransferStack', {
  env: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: process.env.CDK_DEFAULT_REGION,
  },
  description: 'FastTransfer - High-performance file transfer platform with ZMT compression',
});
