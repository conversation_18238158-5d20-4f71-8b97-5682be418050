import { useState, useEffect } from 'react';
import {
  BarChart3,
  TrendingUp,
  Download,
  Upload,
  Zap,
  FileText,
  Activity,
  RefreshCw,
  HardDrive
} from 'lucide-react';
import { useAnalytics } from '../hooks/useProgressTracking';

interface AnalyticsDashboardProps {
  className?: string;
}

export function AnalyticsDashboard({ className = '' }: AnalyticsDashboardProps) {
  const { summary, loading, error, fetchSummary } = useAnalytics();
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d' | 'all'>('7d');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
  }, [timeRange]);

  const loadData = async () => {
    const now = Date.now();
    let start: number | undefined;
    
    switch (timeRange) {
      case '24h':
        start = now - (24 * 60 * 60 * 1000);
        break;
      case '7d':
        start = now - (7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        start = now - (30 * 24 * 60 * 60 * 1000);
        break;
      default:
        start = undefined;
    }

    const timeRangeParam = start ? { start, end: now } : undefined;
    await Promise.all([
      fetchSummary(timeRangeParam),
      // fetchEvents(50) // TODO: Implement fetchEvents function
    ]);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatSpeed = (bytesPerSecond: number): string => {
    return formatFileSize(bytesPerSecond) + '/s';
  };

  const formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const formatPercentage = (ratio: number): string => {
    return `${Math.round(ratio * 100)}%`;
  };

  if (loading && !summary) {
    return (
      <div className={`bg-base-100 rounded-2xl p-8 shadow-sm border border-base-300 ${className}`}>
        <div className="flex items-center justify-center">
          <div className="loading loading-spinner loading-lg"></div>
          <span className="ml-3 text-base-content/70">Loading analytics...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-base-100 rounded-2xl p-8 shadow-sm border border-base-300 ${className}`}>
        <div className="text-center">
          <Activity className="w-12 h-12 text-error mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-base-content mb-2">Analytics Unavailable</h3>
          <p className="text-base-content/70 mb-4">{error}</p>
          <button onClick={handleRefresh} className="btn btn-primary btn-sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-base-content">Analytics Dashboard</h2>
          <p className="text-base-content/70">Transfer performance and usage statistics</p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Time Range Selector */}
          <div className="join">
            {(['24h', '7d', '30d', 'all'] as const).map((range) => (
              <button
                key={range}
                onClick={() => setTimeRange(range)}
                className={`btn btn-sm join-item ${
                  timeRange === range ? 'btn-primary' : 'btn-outline'
                }`}
              >
                {range === 'all' ? 'All Time' : range.toUpperCase()}
              </button>
            ))}
          </div>
          
          {/* Refresh Button */}
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="btn btn-sm btn-ghost"
          >
            <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {summary && (
        <>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="stat bg-base-100 rounded-2xl shadow-sm border" style={{borderColor: '#313131'}}>
              <div className="stat-figure text-primary">
                <Upload className="w-8 h-8" />
              </div>
              <div className="stat-title">Total Uploads</div>
              <div className="stat-value text-primary">{summary.totalUploads.toLocaleString()}</div>
              <div className="stat-desc">Files processed</div>
            </div>

            <div className="stat bg-base-100 rounded-2xl shadow-sm border" style={{borderColor: '#313131'}}>
              <div className="stat-figure text-secondary">
                <Download className="w-8 h-8" />
              </div>
              <div className="stat-title">Total Downloads</div>
              <div className="stat-value text-secondary">{summary.totalDownloads.toLocaleString()}</div>
              <div className="stat-desc">Files downloaded</div>
            </div>

            <div className="stat bg-base-100 rounded-2xl shadow-sm border" style={{borderColor: '#313131'}}>
              <div className="stat-figure text-accent">
                <HardDrive className="w-8 h-8" />
              </div>
              <div className="stat-title">Data Transferred</div>
              <div className="stat-value text-accent">{formatFileSize(summary.totalDataTransferred)}</div>
              <div className="stat-desc">Total volume</div>
            </div>

            <div className="stat bg-base-100 rounded-2xl shadow-sm border" style={{borderColor: '#313131'}}>
              <div className="stat-figure text-success">
                <Zap className="w-8 h-8" />
              </div>
              <div className="stat-title">Avg Compression</div>
              <div className="stat-value text-success">{formatPercentage(summary.averageCompressionRatio)}</div>
              <div className="stat-desc">Size reduction</div>
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-base-100 rounded-2xl p-6 shadow-sm border" style={{borderColor: '#313131'}}>
              <h3 className="text-lg font-semibold text-base-content mb-4 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Performance Metrics
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-base-content/70">Average Upload Speed</span>
                  <span className="font-medium text-base-content">
                    {formatSpeed(summary.averageUploadSpeed)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-base-content/70">Average Compression Time</span>
                  <span className="font-medium text-base-content">
                    {formatDuration(summary.averageCompressionTime)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-base-content/70">Success Rate</span>
                  <span className="font-medium text-success">
                    {formatPercentage(summary.successRate)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-base-content/70">Error Rate</span>
                  <span className="font-medium text-error">
                    {formatPercentage(summary.errorRate)}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-base-100 rounded-2xl p-6 shadow-sm border border-base-300">
              <h3 className="text-lg font-semibold text-base-content mb-4 flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Top File Types
              </h3>
              <div className="space-y-3">
                {summary.topFileTypes.slice(0, 5).map((fileType: any, index: number) => (
                  <div key={fileType.type} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 rounded-full bg-primary" style={{
                        backgroundColor: `hsl(${(index * 60) % 360}, 70%, 50%)`
                      }} />
                      <span className="text-base-content font-mono text-sm">
                        .{fileType.type}
                      </span>
                    </div>
                    <div className="text-right">
                      <span className="font-medium text-base-content">{fileType.count}</span>
                      <span className="text-xs text-base-content/60 ml-1">files</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Daily Activity Chart */}
          {summary.dailyStats && summary.dailyStats.length > 0 && (
            <div className="bg-base-100 rounded-2xl p-6 shadow-sm border" style={{borderColor: '#313131'}}>
              <h3 className="text-lg font-semibold text-base-content mb-4 flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Daily Activity
              </h3>
              <div className="space-y-2">
                {summary.dailyStats.slice(-7).map((day: any) => {
                  const maxTransfers = Math.max(...summary.dailyStats.map((d: any) => d.transfers));
                  const percentage = maxTransfers > 0 ? (day.transfers / maxTransfers) * 100 : 0;
                  
                  return (
                    <div key={day.date} className="flex items-center space-x-4">
                      <div className="w-20 text-sm text-base-content/70">
                        {new Date(day.date).toLocaleDateString('en-US', { 
                          month: 'short', 
                          day: 'numeric' 
                        })}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-base-content">{day.transfers} transfers</span>
                          <span className="text-xs text-base-content/60">
                            {formatFileSize(day.dataTransferred)}
                          </span>
                        </div>
                        <progress 
                          className="progress progress-primary w-full h-2" 
                          value={percentage} 
                          max="100"
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
