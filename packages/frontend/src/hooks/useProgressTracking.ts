import { useState, useEffect, useCallback } from 'react';
import { getAnalyticsSummary, getAnalyticsEvents, getPerformanceMetrics, type AnalyticsSummary, type AnalyticsEvent, type PerformanceMetrics, type TimeRange } from '../services/api';

export interface ProgressState {
  phase: 'idle' | 'uploading' | 'compressing' | 'ready' | 'error';
  uploadProgress: number;
  compressionProgress: number;
  speed: number; // bytes per second
  timeRemaining: number; // seconds
  totalTime: number; // seconds since start
  error?: string;
}

export interface ProgressMetrics {
  startTime: number;
  uploadStartTime?: number;
  uploadEndTime?: number;
  compressionStartTime?: number;
  compressionEndTime?: number;
  fileSize: number;
  bytesUploaded: number;
  estimatedCompressionTime?: number;
}

export function useProgressTracking(_transferId?: string) {
  const [progress, setProgress] = useState<ProgressState>({
    phase: 'idle',
    uploadProgress: 0,
    compressionProgress: 0,
    speed: 0,
    timeRemaining: 0,
    totalTime: 0
  });

  const [metrics, setMetrics] = useState<ProgressMetrics>({
    startTime: 0,
    fileSize: 0,
    bytesUploaded: 0
  });

  // Start tracking for a new upload
  const startTracking = useCallback((fileSize: number) => {
    const now = Date.now();
    setMetrics({
      startTime: now,
      uploadStartTime: now,
      fileSize,
      bytesUploaded: 0
    });
    setProgress({
      phase: 'uploading',
      uploadProgress: 0,
      compressionProgress: 0,
      speed: 0,
      timeRemaining: 0,
      totalTime: 0
    });
  }, []);

  // Update upload progress
  const updateUploadProgress = useCallback((bytesUploaded: number) => {
    setMetrics(prev => {
      const now = Date.now();
      const uploadTime = (now - (prev.uploadStartTime || prev.startTime)) / 1000;
      const speed = uploadTime > 0 ? bytesUploaded / uploadTime : 0;
      const uploadProgress = prev.fileSize > 0 ? (bytesUploaded / prev.fileSize) * 100 : 0;
      const remainingBytes = prev.fileSize - bytesUploaded;
      const timeRemaining = speed > 0 ? remainingBytes / speed : 0;

      setProgress(prevProgress => ({
        ...prevProgress,
        uploadProgress: Math.min(uploadProgress, 100),
        speed,
        timeRemaining,
        totalTime: uploadTime
      }));

      return {
        ...prev,
        bytesUploaded
      };
    });
  }, []);

  // Mark upload as complete and start compression
  const startCompression = useCallback(() => {
    const now = Date.now();
    setMetrics(prev => ({
      ...prev,
      uploadEndTime: now,
      compressionStartTime: now,
      estimatedCompressionTime: 5 // Default 5 seconds estimate
    }));
    setProgress(prev => ({
      ...prev,
      phase: 'compressing',
      uploadProgress: 100,
      compressionProgress: 0
    }));
  }, []);

  // Update compression progress (estimated)
  const updateCompressionProgress = useCallback(() => {
    setMetrics(prev => {
      if (!prev.compressionStartTime || !prev.estimatedCompressionTime) return prev;
      
      const now = Date.now();
      const compressionTime = (now - prev.compressionStartTime) / 1000;
      const compressionProgress = Math.min((compressionTime / prev.estimatedCompressionTime) * 100, 95);
      const totalTime = (now - prev.startTime) / 1000;

      setProgress(prevProgress => ({
        ...prevProgress,
        compressionProgress,
        totalTime
      }));

      return prev;
    });
  }, []);

  // Mark as complete
  const markComplete = useCallback((_compressionRatio?: number) => {
    const now = Date.now();
    setMetrics(prev => ({
      ...prev,
      compressionEndTime: now
    }));
    setProgress(prev => ({
      ...prev,
      phase: 'ready',
      compressionProgress: 100,
      totalTime: (now - metrics.startTime) / 1000
    }));
  }, [metrics.startTime]);

  // Mark as error
  const markError = useCallback((error: string) => {
    setProgress(prev => ({
      ...prev,
      phase: 'error',
      error
    }));
  }, []);

  // Reset tracking
  const reset = useCallback(() => {
    setProgress({
      phase: 'idle',
      uploadProgress: 0,
      compressionProgress: 0,
      speed: 0,
      timeRemaining: 0,
      totalTime: 0
    });
    setMetrics({
      startTime: 0,
      fileSize: 0,
      bytesUploaded: 0
    });
  }, []);

  // Auto-update compression progress during compression phase
  useEffect(() => {
    if (progress.phase === 'compressing') {
      const interval = setInterval(updateCompressionProgress, 500);
      return () => clearInterval(interval);
    }
  }, [progress.phase, updateCompressionProgress]);

  // Format helpers
  const formatSpeed = useCallback((bytesPerSecond: number): string => {
    if (bytesPerSecond === 0) return '0 B/s';
    const k = 1024;
    const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
    const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k));
    return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }, []);

  const formatTime = useCallback((seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  }, []);

  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }, []);

  return {
    progress,
    metrics,
    startTracking,
    updateUploadProgress,
    startCompression,
    updateCompressionProgress,
    markComplete,
    markError,
    reset,
    formatSpeed,
    formatTime,
    formatFileSize
  };
}

// Hook for fetching analytics data
export function useAnalytics() {
  const [summary, setSummary] = useState<AnalyticsSummary | null>(null);
  const [events, setEvents] = useState<AnalyticsEvent[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSummary = useCallback(async (timeRange?: TimeRange) => {
    try {
      setLoading(true);
      setError(null);

      const data = await getAnalyticsSummary(timeRange);
      setSummary(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchEvents = useCallback(async (limit: number = 100) => {
    try {
      setLoading(true);
      setError(null);

      const data = await getAnalyticsEvents(limit);
      setEvents(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchPerformanceMetrics = useCallback(async (transferId: string): Promise<PerformanceMetrics | null> => {
    try {
      return await getPerformanceMetrics(transferId);
    } catch (err) {
      console.error('Error fetching performance metrics:', err);
      return null;
    }
  }, []);

  return {
    summary,
    events,
    loading,
    error,
    fetchSummary,
    fetchEvents,
    fetchPerformanceMetrics
  };
}
