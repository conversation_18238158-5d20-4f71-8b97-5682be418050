@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global font family */
body {
  font-family: 'Bricolage Grotesque', system-ui, sans-serif;
}

/* CSS Custom Properties */
:root {
  --color-primary: #1FE183;
  --color-border: #313131;
}

/* Apply the custom theme */
[data-theme="fasttransfer"] {
  color-scheme: dark;
  --color-primary: #1FE183;
  --color-border: #313131;
}

/* Ensure primary buttons use the correct color */
.btn-primary {
  background-color: #1FE183 !important;
  border-color: #1FE183 !important;
  color: #000000 !important;
}

.btn-primary:hover {
  background-color: #1BC76B !important;
  border-color: #1BC76B !important;
  color: #000000 !important;
}

/* Apply primary color to elements using CSS custom property */
.text-primary-custom {
  color: var(--color-primary);
}

.bg-primary-custom {
  background-color: var(--color-primary);
}

.border-primary-custom {
  border-color: var(--color-primary);
}

.border-custom {
  border-color: var(--color-border);
}

/* Table styling */
tbody {
  background-color: #313131 !important;
}

tr {
  border-color: #000000 !important;
}

td {
  border-color: #000000 !important;
  border-width: 1px !important;
}

th {
  background-color: #262626 !important;
  border-color: #000000 !important;
  border-width: 1px !important;
}

/* Ensure table styling applies to all tables */
table tbody {
  background-color: #313131 !important;
}

table tr {
  border-color: #000000 !important;
}

table td {
  border-color: #000000 !important;
  border-width: 1px !important;
}

table th {
  background-color: #262626 !important;
  border-color: #000000 !important;
  border-width: 1px !important;
}
