#!/bin/bash

# FastTransfer ZMT Installation Script
# This script sets up the ZMT compression tool on Ubuntu EC2 instances

set -e

echo "Installing ZMT compression tool..."

# Create application directory
sudo mkdir -p /opt/fasttransfer
cd /opt/fasttransfer

# Download ZMT binary (placeholder - replace with actual ZMT download)
# For now, we'll create a wrapper script that uses tar.gz
cat > /tmp/zmt << 'EOF'
#!/bin/bash

# ZMT Compression Tool Wrapper
# Usage: ./zmt [c|x] input output

if [ "$#" -ne 3 ]; then
    echo "Usage: $0 [c|x] input output"
    echo "  c: compress"
    echo "  x: extract"
    exit 1
fi

MODE=$1
INPUT=$2
OUTPUT=$3

case $MODE in
    c|compress)
        echo "Compressing $INPUT to $OUTPUT..."
        if [ -d "$INPUT" ]; then
            # Compress directory
            tar -czf "$OUTPUT" -C "$(dirname "$INPUT")" "$(basename "$INPUT")"
        else
            # Compress single file
            tar -czf "$OUTPUT" -C "$(dirname "$INPUT")" "$(basename "$INPUT")"
        fi
        echo "Compression completed: $OUTPUT"
        ;;
    x|extract)
        echo "Extracting $INPUT to $OUTPUT..."
        mkdir -p "$OUTPUT"
        tar -xzf "$INPUT" -C "$OUTPUT"
        echo "Extraction completed: $OUTPUT"
        ;;
    *)
        echo "Invalid mode: $MODE"
        echo "Use 'c' for compress or 'x' for extract"
        exit 1
        ;;
esac
EOF

# Install the ZMT wrapper
sudo mv /tmp/zmt /opt/fasttransfer/zmt
sudo chmod +x /opt/fasttransfer/zmt

# Create symlink for global access
sudo ln -sf /opt/fasttransfer/zmt /usr/local/bin/zmt

# Test the installation
echo "Testing ZMT installation..."
/opt/fasttransfer/zmt || echo "ZMT installed successfully"

echo "ZMT installation completed!"

# Install Node.js and dependencies for the worker service
echo "Installing Node.js dependencies..."

# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version

echo "Node.js installation completed!"

# Install system dependencies
echo "Installing system dependencies..."
sudo apt-get update
sudo apt-get install -y \
    build-essential \
    python3 \
    python3-pip \
    awscli \
    unzip \
    curl \
    wget

echo "System dependencies installed!"

# Set up log rotation for worker logs
sudo tee /etc/logrotate.d/fasttransfer-worker > /dev/null << 'EOF'
/var/log/fasttransfer-worker.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 ec2-user ec2-user
    postrotate
        systemctl reload fasttransfer-worker || true
    endscript
}
EOF

echo "Log rotation configured!"

# Create worker service directory
sudo mkdir -p /opt/fasttransfer/worker
sudo chown ec2-user:ec2-user /opt/fasttransfer/worker

echo "ZMT and worker environment setup completed!"
echo "Next steps:"
echo "1. Copy worker application files to /opt/fasttransfer/worker"
echo "2. Run 'npm install' in the worker directory"
echo "3. Configure environment variables"
echo "4. Start the fasttransfer-worker service"
