{"name": "fast-transfer-worker", "version": "1.0.0", "description": "FastTransfer EC2 worker service for ZMT compression/decompression", "main": "dist/worker.js", "scripts": {"build": "tsc", "start": "node dist/worker.js", "dev": "ts-node src/worker.ts", "test": "jest"}, "dependencies": {"@aws-sdk/client-s3": "^3.400.0", "@aws-sdk/client-sqs": "^3.400.0", "@aws-sdk/client-dynamodb": "^3.400.0", "@aws-sdk/lib-dynamodb": "^3.400.0", "uuid": "^9.0.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "child_process": "^1.0.2"}, "devDependencies": {"@types/node": "^20.6.0", "@types/uuid": "^9.0.4", "@types/fs-extra": "^11.0.1", "typescript": "^5.2.2", "ts-node": "^10.9.1", "jest": "^29.7.0"}}