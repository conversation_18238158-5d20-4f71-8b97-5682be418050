# SendGrid Email Setup Guide

This guide explains how to set up SendGrid for the FastTransfer email sharing functionality.

## Overview

The FastTransfer platform now uses SendGrid instead of AWS SES for sending email notifications when users share files. The new implementation includes:

- **Multiple file support**: Emails show detailed information for each file in a transfer
- **File size information**: Individual file sizes and total transfer size
- **Compression details**: Shows compression ratio and size savings
- **Enhanced templates**: Beautiful HTML and plain text email templates

## Required Environment Variables

### 1. SENDGRID_API_KEY
Your SendGrid API key with Mail Send permissions.

```bash
export SENDGRID_API_KEY="SG.your-actual-api-key-here"
```

### 2. FROM_EMAIL (Optional)
The verified sender email address. Defaults to `<EMAIL>` if not set.

```bash
export FROM_EMAIL="<EMAIL>"
```

## SendGrid Account Setup

### Step 1: Create SendGrid Account
1. Go to [SendGrid.com](https://sendgrid.com)
2. Sign up for a free account (100 emails/day free tier)
3. Verify your email address

### Step 2: Create API Key
1. Go to Settings → API Keys
2. Click "Create API Key"
3. Choose "Restricted Access"
4. Enable only "Mail Send" permission
5. Copy the generated API key (starts with "SG.")

### Step 3: Verify Sender Identity
1. Go to Settings → Sender Authentication
2. Choose "Single Sender Verification" for testing
3. Add your sender email address
4. Verify the email address via the confirmation email

### Step 4: (Production) Domain Authentication
For production use, set up domain authentication:
1. Go to Settings → Sender Authentication
2. Choose "Domain Authentication"
3. Follow the DNS setup instructions

## Testing the Integration

### Run the Test Script
```bash
# Set environment variables
export SENDGRID_API_KEY="SG.your-actual-api-key-here"
export FROM_EMAIL="<EMAIL>"

# Run the test
node test-sendgrid.js
```

### Update Test Email
Edit `test-sendgrid.js` and change the recipient email:
```javascript
recipientEmail: '<EMAIL>', // Change this to your email
```

## Email Template Features

The new SendGrid integration includes enhanced email templates with:

### HTML Template
- Modern, responsive design
- File list with individual sizes
- Compression information display
- Download button with clear call-to-action
- Expiration and download limit warnings
- FastTransfer branding

### Plain Text Template
- Clean, readable format
- All file information included
- Download link clearly displayed
- Fallback for email clients that don't support HTML

## Example Email Content

For a transfer with multiple files, the email will include:

```
Files Shared with You!

John Doe has shared 3 files with you:

Total: 3 files (3.4 MB)
Compression: 34.2% smaller than original (5.2 MB → 3.4 MB)

Files:
  - document.pdf (2.0 MB)
  - image.jpg (1.0 MB)  
  - data.csv (500.0 KB)

Download Link: https://fasttransfer.com/share/transfer-123
```

## Troubleshooting

### Common Issues

1. **"API key does not start with SG."**
   - Make sure you're using a SendGrid API key, not another service
   - API keys should start with "SG."

2. **"The from address does not match a verified Sender Identity"**
   - Verify your sender email in SendGrid dashboard
   - Make sure FROM_EMAIL matches exactly

3. **"Permission denied"**
   - Check that your API key has "Mail Send" permissions
   - Recreate the API key if needed

4. **Emails not delivered**
   - Check SendGrid Activity dashboard
   - Verify recipient email is valid
   - Check spam folder

### Support
- SendGrid Documentation: https://docs.sendgrid.com
- SendGrid Support: https://support.sendgrid.com
