import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { 
  createOrGetUser, 
  validateUserUpload, 
  completeUserAccount,
  updateUserDataUsage,
  getUserByEmail,
  DATA_LIMITS
} from '../lib/user-service';
import { sendWelcomeEmail, sendUpgradeLimitEmail } from '../lib/email-service';
import { userCreationLimiter, validate, sanitize } from '../lib/rate-limiter';

// Mock AWS clients
jest.mock('../lib/aws-clients', () => ({
  docClient: {
    send: jest.fn()
  },
  ENV: {
    USER_TABLE: 'test-users-table',
    TRANSFER_TABLE: 'test-transfers-table',
    AWS_REGION: 'us-east-1',
    BASE_URL: 'https://test.fasttransfer.com'
  }
}));

// Mock SendGrid
jest.mock('@sendgrid/mail', () => ({
  setApiKey: jest.fn(),
  send: jest.fn().mockResolvedValue([{ statusCode: 202 }])
}));

describe('User System Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('User Creation Flow', () => {
    it('should create a new pending user with correct defaults', async () => {
      const mockSend = require('../lib/aws-clients').docClient.send;
      
      // Mock DynamoDB responses
      mockSend
        .mockResolvedValueOnce({ Item: null }) // GetCommand - user doesn't exist
        .mockResolvedValueOnce({}); // PutCommand - user created

      const email = '<EMAIL>';
      const result = await createOrGetUser(email);

      expect(result.isNew).toBe(true);
      expect(result.user.email).toBe(email);
      expect(result.user.status).toBe('pending');
      expect(result.user.dataUploaded).toBe(0);
      expect(mockSend).toHaveBeenCalledTimes(2);
    });

    it('should return existing user if already exists', async () => {
      const mockSend = require('../lib/aws-clients').docClient.send;
      
      const existingUser = {
        email: '<EMAIL>',
        status: 'pending',
        dataUploaded: 1000,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      mockSend.mockResolvedValueOnce({ Item: existingUser });

      const result = await createOrGetUser('<EMAIL>');

      expect(result.isNew).toBe(false);
      expect(result.user.email).toBe('<EMAIL>');
      expect(mockSend).toHaveBeenCalledTimes(1);
    });

    it('should validate email format correctly', () => {
      expect(validate.email('<EMAIL>')).toBe(true);
      expect(validate.email('invalid.email')).toBe(false);
      expect(validate.email('test@')).toBe(false);
      expect(validate.email('@example.com')).toBe(false);
      expect(validate.email('')).toBe(false);
    });

    it('should sanitize email input correctly', () => {
      expect(sanitize.email('  <EMAIL>  ')).toBe('<EMAIL>');
      expect(sanitize.email('<EMAIL>')).toBe('<EMAIL>');
    });
  });

  describe('Data Limits and Validation', () => {
    it('should allow upload for pending user within 100MB limit', async () => {
      const mockSend = require('../lib/aws-clients').docClient.send;
      
      const user = {
        email: '<EMAIL>',
        status: 'pending',
        dataUploaded: 50 * 1024 * 1024, // 50MB used
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      mockSend.mockResolvedValueOnce({ Item: user });

      const uploadSize = 30 * 1024 * 1024; // 30MB upload
      const validation = await validateUserUpload('<EMAIL>', uploadSize);

      expect(validation.canUpload).toBe(true);
      expect(validation.upgradeRequired).toBe(false);
      expect(validation.remainingBytes).toBe(DATA_LIMITS.PENDING_USER_LIMIT - user.dataUploaded);
    });

    it('should reject upload for pending user exceeding 100MB limit', async () => {
      const mockSend = require('../lib/aws-clients').docClient.send;
      
      const user = {
        email: '<EMAIL>',
        status: 'pending',
        dataUploaded: 90 * 1024 * 1024, // 90MB used
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      mockSend.mockResolvedValueOnce({ Item: user });

      const uploadSize = 20 * 1024 * 1024; // 20MB upload (would exceed limit)
      const validation = await validateUserUpload('<EMAIL>', uploadSize);

      expect(validation.canUpload).toBe(false);
      expect(validation.upgradeRequired).toBe(true);
      expect(validation.message).toContain('upgrade');
    });

    it('should allow large uploads for active users', async () => {
      const mockSend = require('../lib/aws-clients').docClient.send;
      
      const user = {
        email: '<EMAIL>',
        status: 'active',
        dataUploaded: 500 * 1024 * 1024 * 1024, // 500GB used
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      mockSend.mockResolvedValueOnce({ Item: user });

      const uploadSize = 100 * 1024 * 1024 * 1024; // 100GB upload
      const validation = await validateUserUpload('<EMAIL>', uploadSize);

      expect(validation.canUpload).toBe(true);
      expect(validation.upgradeRequired).toBe(false);
    });

    it('should update user data usage correctly', async () => {
      const mockSend = require('../lib/aws-clients').docClient.send;
      mockSend.mockResolvedValueOnce({});

      const email = '<EMAIL>';
      const uploadSize = 50 * 1024 * 1024; // 50MB

      await updateUserDataUsage(email, uploadSize);

      expect(mockSend).toHaveBeenCalledWith(
        expect.objectContaining({
          TableName: 'test-users-table',
          Key: { email },
          UpdateExpression: expect.stringContaining('dataUploaded'),
          ExpressionAttributeValues: expect.objectContaining({
            ':uploadSize': uploadSize
          })
        })
      );
    });
  });

  describe('Account Completion', () => {
    it('should upgrade pending user to active status', async () => {
      const mockSend = require('../lib/aws-clients').docClient.send;
      
      const pendingUser = {
        email: '<EMAIL>',
        status: 'pending',
        dataUploaded: 1000,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      const activeUser = {
        ...pendingUser,
        status: 'active',
        name: 'Test User',
        activatedAt: Date.now()
      };

      mockSend
        .mockResolvedValueOnce({ Item: pendingUser }) // GetCommand
        .mockResolvedValueOnce({}) // UpdateCommand
        .mockResolvedValueOnce({ Item: activeUser }); // GetCommand for return

      const result = await completeUserAccount('<EMAIL>', 'Test User');

      expect(result.status).toBe('active');
      expect(result.name).toBe('Test User');
      expect(mockSend).toHaveBeenCalledTimes(3);
    });

    it('should reject completion for already active user', async () => {
      const mockSend = require('../lib/aws-clients').docClient.send;
      
      const activeUser = {
        email: '<EMAIL>',
        status: 'active',
        dataUploaded: 1000,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      mockSend.mockResolvedValueOnce({ Item: activeUser });

      await expect(
        completeUserAccount('<EMAIL>', 'Test User')
      ).rejects.toThrow('User account is already active or not found');
    });
  });

  describe('Email System', () => {
    it('should send welcome email with correct content', async () => {
      const mockSend = require('@sendgrid/mail').send;
      
      const request = {
        email: '<EMAIL>',
        verificationToken: 'test-token-123',
        baseUrl: 'https://test.fasttransfer.com'
      };

      const result = await sendWelcomeEmail(request);

      expect(result.success).toBe(true);
      expect(mockSend).toHaveBeenCalledWith(
        expect.objectContaining({
          to: request.email,
          subject: expect.stringContaining('1TB'),
          html: expect.stringContaining('complete-account'),
          text: expect.stringContaining('complete-account')
        })
      );
    });

    it('should send upgrade limit email when user hits limit', async () => {
      const mockSend = require('@sendgrid/mail').send;
      
      const request = {
        email: '<EMAIL>',
        dataUsed: 100 * 1024 * 1024, // 100MB
        dataLimit: 100 * 1024 * 1024, // 100MB
        baseUrl: 'https://test.fasttransfer.com'
      };

      const result = await sendUpgradeLimitEmail(request);

      expect(result.success).toBe(true);
      expect(mockSend).toHaveBeenCalledWith(
        expect.objectContaining({
          to: request.email,
          subject: expect.stringContaining('Upgrade'),
          html: expect.stringContaining('100MB'),
          text: expect.stringContaining('100MB')
        })
      );
    });
  });

  describe('Input Validation and Sanitization', () => {
    it('should validate name input correctly', () => {
      expect(validate.name('John Doe')).toBe(true);
      expect(validate.name('A')).toBe(true);
      expect(validate.name('')).toBe(false);
      expect(validate.name('A'.repeat(101))).toBe(false);
    });

    it('should validate password input correctly', () => {
      expect(validate.password('')).toBe(true); // Optional
      expect(validate.password('password123')).toBe(true);
      expect(validate.password('short')).toBe(false);
    });

    it('should sanitize name input correctly', () => {
      expect(sanitize.name('  John Doe  ')).toBe('John Doe');
      expect(sanitize.name('John<script>alert("xss")</script>Doe')).toBe('JohnscriptalertxssscriptDoe');
    });

    it('should validate file size correctly', () => {
      expect(validate.fileSize(1024)).toBe(true);
      expect(validate.fileSize(50 * 1024 * 1024 * 1024)).toBe(true); // 50GB
      expect(validate.fileSize(0)).toBe(false);
      expect(validate.fileSize(-1)).toBe(false);
      expect(validate.fileSize(51 * 1024 * 1024 * 1024)).toBe(false); // > 50GB
    });
  });
});
