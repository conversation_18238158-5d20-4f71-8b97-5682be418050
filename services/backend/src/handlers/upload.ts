import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { SendMessageCommand } from '@aws-sdk/client-sqs';

import { s3Client, docClient, sqsClient, ENV } from '../lib/aws-clients';
import {
  generateTransferId,
  generateS3Key,
  calculateExpirationTime,
  createApiResponse,
  createErrorResponse,
  hashPassword
} from '../lib/utils';
import {
  createOrGetUser,
  validateUserUpload,
  updateUserDataUsage,
  generateVerificationToken
} from '../lib/user-service';
import { sendWelcomeEmail, sendUpgradeLimitEmail } from '../lib/email-service';
import {
  UploadRequest,
  UploadResponse,
  Transfer,
  TransferStatus,
  FileInfo
} from '../types';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (event.httpMethod === 'OPTIONS') {
      return createApiResponse(200, {});
    }

    if (event.httpMethod !== 'POST') {
      return createErrorResponse(405, 'Method not allowed');
    }

    if (!event.body) {
      return createErrorResponse(400, 'Request body is required');
    }

    const request: UploadRequest = JSON.parse(event.body);

    // Validate request
    if (!request.files || !Array.isArray(request.files) || request.files.length === 0) {
      return createErrorResponse(400, 'Files array is required and cannot be empty');
    }

    if (!request.email) {
      return createErrorResponse(400, 'Email address is required for file sharing');
    }

    // Validate file size limits (50GB total)
    const totalSize = request.files.reduce((sum, file) => sum + file.fileSize, 0);
    const maxSize = 50 * 1024 * 1024 * 1024; // 50GB
    if (totalSize > maxSize) {
      return createErrorResponse(400, `Total file size exceeds 50GB limit`);
    }

    // Create or get user account
    let userResult;
    try {
      userResult = await createOrGetUser(request.email);
    } catch (error: any) {
      if (error.message === 'Invalid email format') {
        return createErrorResponse(400, 'Please provide a valid email address');
      }
      throw error;
    }

    // Validate user can upload this amount of data
    const validation = await validateUserUpload(request.email, totalSize);
    if (!validation.canUpload) {
      // Send upgrade email if user is pending and hit limit
      if (validation.upgradeRequired) {
        const baseUrl = ENV.BASE_URL || 'https://fasttransfer.com';
        await sendUpgradeLimitEmail({
          email: request.email,
          dataUsed: userResult.user.dataUploaded,
          dataLimit: 100 * 1024 * 1024, // 100MB
          baseUrl,
        });
      }

      return createErrorResponse(403, validation.message || 'Upload limit exceeded', {
        upgradeRequired: validation.upgradeRequired,
        remainingBytes: validation.remainingBytes,
      });
    }

    // Generate transfer ID and prepare file info
    const transferId = generateTransferId();
    const expiresAt = calculateExpirationTime(request.expirationHours);
    
    const fileInfos: FileInfo[] = request.files.map(file => ({
      fileName: file.fileName,
      fileSize: file.fileSize,
      contentType: file.contentType,
      s3Key: generateS3Key(transferId, file.fileName),
    }));

    // Generate pre-signed upload URLs
    const uploadUrls = await Promise.all(
      fileInfos.map(async (fileInfo) => {
        const command = new PutObjectCommand({
          Bucket: ENV.UPLOAD_BUCKET,
          Key: fileInfo.s3Key,
          ContentType: fileInfo.contentType,
          Metadata: {
            'original-filename': fileInfo.fileName,
            'transfer-id': transferId,
          },
        });

        const uploadUrl = await getSignedUrl(s3Client, command, { 
          expiresIn: 3600 // 1 hour to complete upload
        });

        return {
          fileName: fileInfo.fileName,
          uploadUrl,
          s3Key: fileInfo.s3Key,
        };
      })
    );

    // Create transfer record in DynamoDB
    const transfer: Transfer = {
      transferId,
      originalFiles: fileInfos,
      originalSize: totalSize,
      status: TransferStatus.UPLOADING,
      createdAt: Date.now(),
      expiresAt,
      downloadLimit: request.downloadLimit,
      downloadCount: 0,
      password: request.password ? hashPassword(request.password) : undefined,
      email: request.email,
      userId: request.email, // Link transfer to user
    };

    await docClient.send(new PutCommand({
      TableName: ENV.TRANSFER_TABLE,
      Item: transfer,
    }));

    // Update user's data usage
    await updateUserDataUsage(request.email, totalSize);

    // Send welcome email if this is a new user
    if (userResult.isNew) {
      const baseUrl = ENV.BASE_URL || 'https://fasttransfer.com';
      const verificationToken = generateVerificationToken(request.email);

      // Send welcome email (don't wait for it to complete)
      sendWelcomeEmail({
        email: request.email,
        verificationToken,
        baseUrl,
      }).catch(error => {
        console.error('Failed to send welcome email:', error);
        // Don't fail the upload if email fails
      });
    }

    // Prepare response
    const response: UploadResponse = {
      transferId,
      uploadUrls,
      expiresAt,
    };

    return createApiResponse(200, response);

  } catch (error) {
    console.error('Upload handler error:', error);
    return createErrorResponse(500, 'Internal server error', error);
  }
};

// Handler for upload completion notification
export const uploadCompleteHandler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (event.httpMethod !== 'POST') {
      return createErrorResponse(405, 'Method not allowed');
    }

    if (!event.body) {
      return createErrorResponse(400, 'Request body is required');
    }

    const { transferId } = JSON.parse(event.body);

    if (!transferId) {
      return createErrorResponse(400, 'Transfer ID is required');
    }

    // Update transfer status to UPLOADED
    await docClient.send(new PutCommand({
      TableName: ENV.TRANSFER_TABLE,
      Item: {
        transferId,
        status: TransferStatus.UPLOADED,
        updatedAt: Date.now(),
      },
    }));

    // Queue compression job
    await sqsClient.send(new SendMessageCommand({
      QueueUrl: ENV.COMPRESSION_QUEUE_URL,
      MessageBody: JSON.stringify({
        transferId,
        type: 'COMPRESSION',
        timestamp: Date.now(),
      }),
    }));

    return createApiResponse(200, { 
      message: 'Upload completed, compression queued',
      transferId 
    });

  } catch (error) {
    console.error('Upload complete handler error:', error);
    return createErrorResponse(500, 'Internal server error', error);
  }
};
