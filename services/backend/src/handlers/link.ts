import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { GetCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';

import { docClient, ENV } from '../lib/aws-clients';
import { 
  generateShareUrl,
  calculateExpirationTime,
  createApiResponse,
  createErrorResponse,
  hashPassword,
  isExpired
} from '../lib/utils';
import { 
  LinkRequest, 
  LinkResponse, 
  Transfer,
  TransferStatus
} from '../types';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (event.httpMethod === 'OPTIONS') {
      return createApiResponse(200, {});
    }

    if (event.httpMethod !== 'POST') {
      return createErrorResponse(405, 'Method not allowed');
    }

    if (!event.body) {
      return createErrorResponse(400, 'Request body is required');
    }

    const request: LinkRequest = JSON.parse(event.body);

    if (!request.transferId) {
      return createErrorResponse(400, 'Transfer ID is required');
    }

    // Get transfer from DynamoDB
    const getResult = await docClient.send(new GetCommand({
      TableName: ENV.TRANSFER_TABLE,
      Key: { transferId: request.transferId },
    }));

    if (!getResult.Item) {
      return createErrorResponse(404, 'Transfer not found');
    }

    const transfer = getResult.Item as Transfer;

    // Check if transfer is expired
    if (isExpired(transfer.expiresAt)) {
      return createErrorResponse(410, 'Transfer has expired');
    }

    // Check if transfer is ready for sharing
    if (transfer.status !== TransferStatus.READY && transfer.status !== TransferStatus.COMPRESSED) {
      return createErrorResponse(400, `Transfer is not ready for sharing. Current status: ${transfer.status}`);
    }

    // Update transfer with new link settings if provided
    const updates: Partial<Transfer> = {};
    let newExpiresAt = transfer.expiresAt;

    if (request.expirationHours) {
      newExpiresAt = calculateExpirationTime(request.expirationHours);
      updates.expiresAt = newExpiresAt;
    }

    if (request.downloadLimit !== undefined) {
      updates.downloadLimit = request.downloadLimit;
    }

    if (request.password) {
      updates.password = hashPassword(request.password);
    }

    // Update transfer if there are changes
    if (Object.keys(updates).length > 0) {
      
      await docClient.send(new UpdateCommand({
        TableName: ENV.TRANSFER_TABLE,
        Key: { transferId: request.transferId },
        UpdateExpression: `SET ${Object.keys(updates).map(key => `#${key} = :${key}`).join(', ')}`,
        ExpressionAttributeNames: Object.keys(updates).reduce((acc, key) => {
          acc[`#${key}`] = key;
          return acc;
        }, {} as Record<string, string>),
        ExpressionAttributeValues: Object.keys(updates).reduce((acc, key) => {
          acc[`:${key}`] = updates[key as keyof typeof updates];
          return acc;
        }, {} as Record<string, any>),
      }));
    }

    // Generate share URL
    const shareUrl = generateShareUrl(request.transferId, ENV.BASE_URL);

    const response: LinkResponse = {
      transferId: request.transferId,
      shareUrl,
      expiresAt: newExpiresAt,
      downloadLimit: updates.downloadLimit || transfer.downloadLimit,
    };

    return createApiResponse(200, response);

  } catch (error) {
    console.error('Link generation handler error:', error);
    return createErrorResponse(500, 'Internal server error', error);
  }
};

// Handler for getting transfer info (for link preview)
export const getTransferInfoHandler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (event.httpMethod !== 'GET') {
      return createErrorResponse(405, 'Method not allowed');
    }

    const transferId = event.pathParameters?.transferId;
    if (!transferId) {
      return createErrorResponse(400, 'Transfer ID is required');
    }

    // Get transfer from DynamoDB
    const getResult = await docClient.send(new GetCommand({
      TableName: ENV.TRANSFER_TABLE,
      Key: { transferId },
    }));

    if (!getResult.Item) {
      return createErrorResponse(404, 'Transfer not found');
    }

    const transfer = getResult.Item as Transfer;

    // Check if transfer is expired
    if (isExpired(transfer.expiresAt)) {
      await docClient.send(new UpdateCommand({
        TableName: ENV.TRANSFER_TABLE,
        Key: { transferId },
        UpdateExpression: 'SET #status = :status',
        ExpressionAttributeNames: { '#status': 'status' },
        ExpressionAttributeValues: { ':status': TransferStatus.EXPIRED },
      }));
      
      return createErrorResponse(410, 'Transfer has expired');
    }

    // Return public transfer info (without sensitive data)
    const publicInfo = {
      transferId: transfer.transferId,
      status: transfer.status,
      originalSize: transfer.originalSize,
      compressedSize: transfer.compressedSize,
      compressionRatio: transfer.compressionRatio,
      fileCount: transfer.originalFiles.length,
      createdAt: transfer.createdAt,
      expiresAt: transfer.expiresAt,
      downloadCount: transfer.downloadCount,
      downloadLimit: transfer.downloadLimit,
      hasPassword: !!transfer.password,
      files: transfer.originalFiles.map(file => ({
        fileName: file.fileName,
        fileSize: file.fileSize,
        contentType: file.contentType,
      })),
    };

    return createApiResponse(200, publicInfo);

  } catch (error) {
    console.error('Get transfer info handler error:', error);
    return createErrorResponse(500, 'Internal server error', error);
  }
};
