import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { GetCommand, UpdateCommand, PutCommand } from '@aws-sdk/lib-dynamodb';
import { SendMessageCommand } from '@aws-sdk/client-sqs';

import { docClient, sqsClient, ENV } from '../lib/aws-clients';
import { 
  createApiResponse,
  createErrorResponse,
  validatePassword,
  isExpired,
  generateJobId
} from '../lib/utils';
import { 
  DownloadRequest, 
  DownloadResponse, 
  Transfer,
  TransferStatus,
  Job,
  JobType,
  JobStatus
} from '../types';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (event.httpMethod === 'OPTIONS') {
      return createApiResponse(200, {});
    }

    if (event.httpMethod !== 'POST') {
      return createErrorResponse(405, 'Method not allowed');
    }

    const transferId = event.pathParameters?.transferId;
    if (!transferId) {
      return createErrorResponse(400, 'Transfer ID is required');
    }

    // Parse request body for password if provided
    let password: string | undefined;
    if (event.body) {
      const request: DownloadRequest = JSON.parse(event.body);
      password = request.password;
    }

    // Get transfer from DynamoDB
    const getResult = await docClient.send(new GetCommand({
      TableName: ENV.TRANSFER_TABLE,
      Key: { transferId },
    }));

    if (!getResult.Item) {
      return createErrorResponse(404, 'Transfer not found');
    }

    const transfer = getResult.Item as Transfer;

    // Check if transfer is expired
    if (isExpired(transfer.expiresAt)) {
      await docClient.send(new UpdateCommand({
        TableName: ENV.TRANSFER_TABLE,
        Key: { transferId },
        UpdateExpression: 'SET #status = :status',
        ExpressionAttributeNames: { '#status': 'status' },
        ExpressionAttributeValues: { ':status': TransferStatus.EXPIRED },
      }));
      
      return createErrorResponse(410, 'Transfer has expired');
    }

    // Check password if required
    if (transfer.password && (!password || !validatePassword(password, transfer.password))) {
      return createErrorResponse(401, 'Invalid password');
    }

    // Check download limit
    if (transfer.downloadLimit && transfer.downloadCount >= transfer.downloadLimit) {
      return createErrorResponse(403, 'Download limit exceeded');
    }

    // Handle different transfer statuses
    switch (transfer.status) {
      case TransferStatus.UPLOADING:
      case TransferStatus.UPLOADED:
      case TransferStatus.COMPRESSING:
        return createErrorResponse(202, 'Transfer is still being processed', {
          status: transfer.status,
          message: 'Please try again later'
        });

      case TransferStatus.COMPRESSED:
        // Need to decompress files first
        await initiateDecompression(transferId);
        return createApiResponse(202, {
          status: TransferStatus.DECOMPRESSING,
          message: 'Preparing files for download, please wait...',
          transferId
        });

      case TransferStatus.DECOMPRESSING:
        return createApiResponse(202, {
          status: TransferStatus.DECOMPRESSING,
          message: 'Files are being prepared, please wait...',
          transferId
        });

      case TransferStatus.READY:
        // Files are ready for download
        break;

      case TransferStatus.ERROR:
        return createErrorResponse(500, 'Transfer processing failed');

      default:
        return createErrorResponse(400, `Invalid transfer status: ${transfer.status}`);
    }

    // Increment download count
    await docClient.send(new UpdateCommand({
      TableName: ENV.TRANSFER_TABLE,
      Key: { transferId },
      UpdateExpression: 'SET downloadCount = downloadCount + :inc',
      ExpressionAttributeValues: { ':inc': 1 },
    }));

    // Generate download URLs for each file
    const files = transfer.originalFiles.map(file => ({
      fileName: file.fileName,
      downloadUrl: `${ENV.CLOUDFRONT_DOMAIN}/${transferId}/${file.fileName}`,
      fileSize: file.fileSize,
    }));

    const response: DownloadResponse = {
      transferId,
      files,
      status: transfer.status,
    };

    return createApiResponse(200, response);

  } catch (error) {
    console.error('Download handler error:', error);
    return createErrorResponse(500, 'Internal server error', error);
  }
};

async function initiateDecompression(transferId: string): Promise<void> {
  const jobId = generateJobId();
  
  // Create decompression job record
  const job: Job = {
    jobId,
    transferId,
    type: JobType.DECOMPRESSION,
    status: JobStatus.PENDING,
    createdAt: Date.now(),
    updatedAt: Date.now(),
  };

  await docClient.send(new PutCommand({
    TableName: ENV.JOB_TABLE,
    Item: job,
  }));

  // Update transfer status
  await docClient.send(new UpdateCommand({
    TableName: ENV.TRANSFER_TABLE,
    Key: { transferId },
    UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt',
    ExpressionAttributeNames: { '#status': 'status' },
    ExpressionAttributeValues: { 
      ':status': TransferStatus.DECOMPRESSING,
      ':updatedAt': Date.now()
    },
  }));

  // Queue decompression job
  await sqsClient.send(new SendMessageCommand({
    QueueUrl: ENV.DECOMPRESSION_QUEUE_URL,
    MessageBody: JSON.stringify({
      jobId,
      transferId,
      type: 'DECOMPRESSION',
      timestamp: Date.now(),
    }),
  }));
}

// Handler for checking download status
export const downloadStatusHandler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (event.httpMethod !== 'GET') {
      return createErrorResponse(405, 'Method not allowed');
    }

    const transferId = event.pathParameters?.transferId;
    if (!transferId) {
      return createErrorResponse(400, 'Transfer ID is required');
    }

    // Get transfer from DynamoDB
    const getResult = await docClient.send(new GetCommand({
      TableName: ENV.TRANSFER_TABLE,
      Key: { transferId },
    }));

    if (!getResult.Item) {
      return createErrorResponse(404, 'Transfer not found');
    }

    const transfer = getResult.Item as Transfer;

    return createApiResponse(200, {
      transferId,
      status: transfer.status,
      isReady: transfer.status === TransferStatus.READY,
    });

  } catch (error) {
    console.error('Download status handler error:', error);
    return createErrorResponse(500, 'Internal server error', error);
  }
};
