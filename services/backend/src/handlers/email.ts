import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { GetCommand } from '@aws-sdk/lib-dynamodb';

import { docClient, ENV } from '../lib/aws-clients';
import { sendShareEmail, EmailShareRequest } from '../lib/email-service';
import { 
  createApiResponse,
  createErrorResponse,
  isExpired
} from '../lib/utils';
import { Transfer, TransferStatus } from '../types';

export interface EmailRequest {
  transferId: string;
  recipientEmail: string;
  senderName?: string;
}

export interface EmailResponse {
  success: boolean;
  error?: string;
}

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (event.httpMethod === 'OPTIONS') {
      return createApiResponse(200, {});
    }

    if (event.httpMethod !== 'POST') {
      return createErrorResponse(405, 'Method not allowed');
    }

    if (!event.body) {
      return createErrorResponse(400, 'Request body is required');
    }

    const request: EmailRequest = JSON.parse(event.body);

    // Validate required fields
    if (!request.transferId) {
      return createErrorResponse(400, 'Transfer ID is required');
    }

    if (!request.recipientEmail) {
      return createErrorResponse(400, 'Recipient email is required');
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(request.recipientEmail)) {
      return createErrorResponse(400, 'Invalid email address format');
    }

    // Get transfer from DynamoDB
    const getResult = await docClient.send(new GetCommand({
      TableName: ENV.TRANSFER_TABLE,
      Key: { transferId: request.transferId },
    }));

    if (!getResult.Item) {
      return createErrorResponse(404, 'Transfer not found');
    }

    const transfer = getResult.Item as Transfer;

    // Check if transfer is ready for sharing
    if (transfer.status !== TransferStatus.READY) {
      return createErrorResponse(400, `Transfer is not ready for sharing. Current status: ${transfer.status}`);
    }

    // Check if transfer is expired
    if (transfer.expiresAt && isExpired(transfer.expiresAt)) {
      return createErrorResponse(410, 'Transfer has expired');
    }

    // Check if download limit has been reached
    if (transfer.downloadLimit && transfer.downloadCount >= transfer.downloadLimit) {
      return createErrorResponse(410, 'Download limit has been reached');
    }

    // Generate download URL
    const shareLink = `${ENV.BASE_URL}/share/${request.transferId}`;

    // Prepare file information for email
    const files = transfer.originalFiles.map(file => ({
      name: file.fileName,
      size: file.fileSize,
      originalSize: file.fileSize // In Lambda context, we don't have separate original sizes per file
    }));

    // Calculate compression ratio if available
    const compressionRatio = transfer.compressionRatio;

    const emailData: EmailShareRequest = {
      transferId: request.transferId,
      recipientEmail: request.recipientEmail,
      senderName: request.senderName,
      shareLink,
      files,
      totalSize: transfer.compressedSize || transfer.originalSize,
      totalOriginalSize: transfer.originalSize,
      compressionRatio,
      expiresAt: new Date(transfer.expiresAt).toISOString(),
      downloadLimit: transfer.downloadLimit || 10,
    };

    // Send email
    const emailResult = await sendShareEmail(emailData);

    if (!emailResult.success) {
      return createErrorResponse(500, `Failed to send email: ${emailResult.error}`);
    }

    const response: EmailResponse = {
      success: true,
    };

    return createApiResponse(200, response);

  } catch (error) {
    console.error('Email handler error:', error);
    return createErrorResponse(500, 'Internal server error', error);
  }
};
