import { v4 as uuidv4 } from 'uuid';

// Analytics Event Types
export enum AnalyticsEventType {
  UPLOAD_STARTED = 'upload_started',
  UPLOAD_COMPLETED = 'upload_completed',
  UPLOAD_FAILED = 'upload_failed',
  COMPRESSION_STARTED = 'compression_started',
  COMPRESSION_COMPLETED = 'compression_completed',
  COMPRESSION_FAILED = 'compression_failed',
  DOWNLOAD_STARTED = 'download_started',
  DOWNLOAD_COMPLETED = 'download_completed',
  DOWNLOAD_FAILED = 'download_failed',
  SHARE_LINK_GENERATED = 'share_link_generated',
  SHARE_EMAIL_SENT = 'share_email_sent',
  FILE_EXPIRED = 'file_expired',
  USER_SESSION_STARTED = 'user_session_started',
  ERROR_OCCURRED = 'error_occurred'
}

// Analytics Event Interface
export interface AnalyticsEvent {
  eventId: string;
  eventType: AnalyticsEventType;
  timestamp: number;
  sessionId?: string;
  transferId?: string;
  userId?: string;
  metadata: {
    fileSize?: number;
    fileName?: string;
    compressionRatio?: number;
    compressionTime?: number;
    transferSpeed?: number;
    errorMessage?: string;
    userAgent?: string;
    ipAddress?: string;
    [key: string]: any;
  };
}

// Performance Metrics Interface
export interface PerformanceMetrics {
  transferId: string;
  uploadStartTime: number;
  uploadEndTime?: number;
  compressionStartTime?: number;
  compressionEndTime?: number;
  downloadStartTime?: number;
  downloadEndTime?: number;
  originalFileSize: number;
  compressedFileSize?: number;
  compressionRatio?: number;
  uploadSpeed?: number; // bytes per second
  compressionSpeed?: number; // bytes per second
  downloadSpeed?: number; // bytes per second
  totalProcessingTime?: number; // milliseconds
}

// Analytics Summary Interface
export interface AnalyticsSummary {
  totalTransfers: number;
  totalUploads: number;
  totalDownloads: number;
  totalDataTransferred: number;
  totalDataCompressed: number;
  averageCompressionRatio: number;
  averageUploadSpeed: number;
  averageCompressionTime: number;
  successRate: number;
  errorRate: number;
  topFileTypes: { type: string; count: number }[];
  dailyStats: { date: string; transfers: number; dataTransferred: number }[];
}

// In-memory storage for analytics (in production, use a database)
class AnalyticsService {
  private events: AnalyticsEvent[] = [];
  private performanceMetrics: Map<string, PerformanceMetrics> = new Map();
  private sessionMetrics: Map<string, any> = new Map();

  // Track an analytics event
  trackEvent(eventType: AnalyticsEventType, metadata: any = {}, transferId?: string, sessionId?: string): void {
    const event: AnalyticsEvent = {
      eventId: uuidv4(),
      eventType,
      timestamp: Date.now(),
      sessionId,
      transferId,
      metadata
    };

    this.events.push(event);
    console.log(`📊 Analytics Event: ${eventType}`, { transferId, metadata });
  }

  // Start tracking performance for a transfer
  startPerformanceTracking(transferId: string, originalFileSize: number): void {
    const metrics: PerformanceMetrics = {
      transferId,
      uploadStartTime: Date.now(),
      originalFileSize
    };
    
    this.performanceMetrics.set(transferId, metrics);
    this.trackEvent(AnalyticsEventType.UPLOAD_STARTED, { fileSize: originalFileSize }, transferId);
  }

  // Update performance metrics
  updatePerformanceMetrics(transferId: string, updates: Partial<PerformanceMetrics>): void {
    const existing = this.performanceMetrics.get(transferId);
    if (existing) {
      const updated = { ...existing, ...updates };
      
      // Calculate derived metrics
      if (updated.uploadEndTime && updated.uploadStartTime) {
        const uploadTime = updated.uploadEndTime - updated.uploadStartTime;
        updated.uploadSpeed = updated.originalFileSize / (uploadTime / 1000); // bytes per second
      }
      
      if (updated.compressionEndTime && updated.compressionStartTime && updated.compressedFileSize) {
        const compressionTime = updated.compressionEndTime - updated.compressionStartTime;
        updated.compressionSpeed = updated.originalFileSize / (compressionTime / 1000);
        updated.compressionRatio = (updated.originalFileSize - updated.compressedFileSize) / updated.originalFileSize;
      }
      
      if (updated.compressionEndTime && updated.uploadStartTime) {
        updated.totalProcessingTime = updated.compressionEndTime - updated.uploadStartTime;
      }
      
      this.performanceMetrics.set(transferId, updated);
    }
  }

  // Get performance metrics for a transfer
  getPerformanceMetrics(transferId: string): PerformanceMetrics | undefined {
    return this.performanceMetrics.get(transferId);
  }

  // Get analytics summary
  getAnalyticsSummary(timeRange?: { start: number; end: number }): AnalyticsSummary {
    const filteredEvents = timeRange 
      ? this.events.filter(e => e.timestamp >= timeRange.start && e.timestamp <= timeRange.end)
      : this.events;

    const uploads = filteredEvents.filter(e => e.eventType === AnalyticsEventType.UPLOAD_COMPLETED);
    const downloads = filteredEvents.filter(e => e.eventType === AnalyticsEventType.DOWNLOAD_COMPLETED);
    const compressions = filteredEvents.filter(e => e.eventType === AnalyticsEventType.COMPRESSION_COMPLETED);
    const errors = filteredEvents.filter(e => e.eventType.includes('failed') || e.eventType === AnalyticsEventType.ERROR_OCCURRED);

    const totalDataTransferred = uploads.reduce((sum, e) => sum + (e.metadata.fileSize || 0), 0);
    const totalDataCompressed = compressions.reduce((sum, e) => sum + (e.metadata.fileSize || 0), 0);
    
    const compressionRatios = compressions
      .map(e => e.metadata.compressionRatio)
      .filter(ratio => ratio !== undefined) as number[];
    
    const averageCompressionRatio = compressionRatios.length > 0 
      ? compressionRatios.reduce((sum, ratio) => sum + ratio, 0) / compressionRatios.length 
      : 0;

    // Calculate file type distribution
    const fileTypes: { [key: string]: number } = {};
    uploads.forEach(e => {
      if (e.metadata.fileName) {
        const extension = e.metadata.fileName.split('.').pop()?.toLowerCase() || 'unknown';
        fileTypes[extension] = (fileTypes[extension] || 0) + 1;
      }
    });

    const topFileTypes = Object.entries(fileTypes)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calculate daily stats (last 30 days)
    const dailyStats: { [key: string]: { transfers: number; dataTransferred: number } } = {};
    const now = Date.now();
    const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);

    uploads.forEach(e => {
      if (e.timestamp >= thirtyDaysAgo) {
        const date = new Date(e.timestamp).toISOString().split('T')[0];
        if (!dailyStats[date]) {
          dailyStats[date] = { transfers: 0, dataTransferred: 0 };
        }
        dailyStats[date].transfers++;
        dailyStats[date].dataTransferred += e.metadata.fileSize || 0;
      }
    });

    const dailyStatsArray = Object.entries(dailyStats)
      .map(([date, stats]) => ({ date, ...stats }))
      .sort((a, b) => a.date.localeCompare(b.date));

    return {
      totalTransfers: uploads.length,
      totalUploads: uploads.length,
      totalDownloads: downloads.length,
      totalDataTransferred,
      totalDataCompressed,
      averageCompressionRatio,
      averageUploadSpeed: this.calculateAverageUploadSpeed(),
      averageCompressionTime: this.calculateAverageCompressionTime(),
      successRate: filteredEvents.length > 0 ? (filteredEvents.length - errors.length) / filteredEvents.length : 0,
      errorRate: filteredEvents.length > 0 ? errors.length / filteredEvents.length : 0,
      topFileTypes,
      dailyStats: dailyStatsArray
    };
  }

  // Get recent events
  getRecentEvents(limit: number = 100): AnalyticsEvent[] {
    return this.events
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  // Get events by type
  getEventsByType(eventType: AnalyticsEventType, limit?: number): AnalyticsEvent[] {
    const filtered = this.events.filter(e => e.eventType === eventType);
    return limit ? filtered.slice(-limit) : filtered;
  }

  // Calculate average upload speed
  private calculateAverageUploadSpeed(): number {
    const metrics = Array.from(this.performanceMetrics.values());
    const speeds = metrics
      .map(m => m.uploadSpeed)
      .filter(speed => speed !== undefined) as number[];
    
    return speeds.length > 0 ? speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length : 0;
  }

  // Calculate average compression time
  private calculateAverageCompressionTime(): number {
    const metrics = Array.from(this.performanceMetrics.values());
    const times = metrics
      .map(m => m.compressionEndTime && m.compressionStartTime 
        ? m.compressionEndTime - m.compressionStartTime 
        : undefined)
      .filter(time => time !== undefined) as number[];
    
    return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 0;
  }

  // Clear old data (for memory management)
  clearOldData(olderThanDays: number = 30): void {
    const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    
    // Remove old events
    this.events = this.events.filter(e => e.timestamp >= cutoffTime);
    
    // Remove old performance metrics
    for (const [transferId, metrics] of this.performanceMetrics.entries()) {
      if (metrics.uploadStartTime < cutoffTime) {
        this.performanceMetrics.delete(transferId);
      }
    }
    
    console.log(`🧹 Cleaned up analytics data older than ${olderThanDays} days`);
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService();

// Helper function to format file size
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Helper function to format speed
export function formatSpeed(bytesPerSecond: number): string {
  return formatFileSize(bytesPerSecond) + '/s';
}

// Helper function to format duration
export function formatDuration(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}
