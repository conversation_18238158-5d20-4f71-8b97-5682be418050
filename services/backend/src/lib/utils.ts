import { v4 as uuidv4 } from 'uuid';
import { createHash } from 'crypto';

export function generateTransferId(): string {
  return uuidv4();
}

export function generateJobId(): string {
  return uuidv4();
}

export function generateS3Key(transferId: string, fileName: string): string {
  return `${transferId}/${fileName}`;
}

export function generateShareUrl(transferId: string, baseUrl: string): string {
  return `${baseUrl}/download/${transferId}`;
}

export function hashPassword(password: string): string {
  return createHash('sha256').update(password).digest('hex');
}

export function validatePassword(password: string, hash: string): boolean {
  return hashPassword(password) === hash;
}

export function calculateExpirationTime(hours: number = 168): number { // Default 7 days
  return Date.now() + (hours * 60 * 60 * 1000);
}

export function isExpired(expiresAt: number): boolean {
  return Date.now() > expiresAt;
}

export function formatFileSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

export function calculateCompressionRatio(originalSize: number, compressedSize: number): number {
  if (originalSize === 0) return 0;
  return Math.round(((originalSize - compressedSize) / originalSize) * 100);
}

export function createApiResponse(statusCode: number, body: any, headers: Record<string, string> = {}) {
  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
      'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
      ...headers,
    },
    body: JSON.stringify(body),
  };
}

export function createErrorResponse(statusCode: number, message: string, error?: any) {
  console.error('API Error:', { statusCode, message, error });
  return createApiResponse(statusCode, {
    error: message,
    ...(process.env.NODE_ENV === 'development' && error && { details: error }),
  });
}
