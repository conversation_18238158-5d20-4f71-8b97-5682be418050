import { GetCommand, PutCommand, UpdateCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { docClient, ENV } from './aws-clients';
import { User, UserStatus } from '../types';
import { createHash, randomBytes } from 'crypto';

// Constants for data limits
export const DATA_LIMITS = {
  PENDING_USER_LIMIT: 100 * 1024 * 1024, // 100MB in bytes
  ACTIVE_USER_LIMIT: 1024 * 1024 * 1024 * 1024, // 1TB in bytes
};

export interface CreateUserResult {
  user: User;
  isNew: boolean;
}

export interface UserValidationResult {
  canUpload: boolean;
  remainingBytes: number;
  upgradeRequired: boolean;
  message?: string;
}

/**
 * Generate a verification token for email verification
 */
export function generateVerificationToken(email: string): string {
  const timestamp = Date.now().toString();
  const random = randomBytes(16).toString('hex');
  const data = `${email}:${timestamp}:${random}`;
  return createHash('sha256').update(data).digest('hex');
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
}

/**
 * Get user by email
 */
export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    const result = await docClient.send(new GetCommand({
      TableName: ENV.USER_TABLE,
      Key: { email: email.toLowerCase() },
    }));

    return result.Item as User || null;
  } catch (error) {
    console.error('Error getting user by email:', error);
    throw new Error('Failed to retrieve user');
  }
}

/**
 * Create a new pending user or return existing user
 */
export async function createOrGetUser(email: string): Promise<CreateUserResult> {
  if (!isValidEmail(email)) {
    throw new Error('Invalid email format');
  }

  const normalizedEmail = email.toLowerCase();
  
  // Check if user already exists
  const existingUser = await getUserByEmail(normalizedEmail);
  if (existingUser) {
    return { user: existingUser, isNew: false };
  }

  // Create new pending user
  const now = Date.now();
  const newUser: User = {
    email: normalizedEmail,
    status: UserStatus.PENDING,
    dataUploaded: 0,
    createdAt: now,
    updatedAt: now,
  };

  try {
    await docClient.send(new PutCommand({
      TableName: ENV.USER_TABLE,
      Item: newUser,
      ConditionExpression: 'attribute_not_exists(email)', // Ensure uniqueness
    }));

    return { user: newUser, isNew: true };
  } catch (error: any) {
    if (error.name === 'ConditionalCheckFailedException') {
      // Race condition - user was created between our check and put
      const existingUser = await getUserByEmail(normalizedEmail);
      if (existingUser) {
        return { user: existingUser, isNew: false };
      }
    }
    console.error('Error creating user:', error);
    throw new Error('Failed to create user account');
  }
}

/**
 * Update user's data uploaded amount
 */
export async function updateUserDataUsage(email: string, additionalBytes: number): Promise<User> {
  const normalizedEmail = email.toLowerCase();
  
  try {
    const result = await docClient.send(new UpdateCommand({
      TableName: ENV.USER_TABLE,
      Key: { email: normalizedEmail },
      UpdateExpression: 'ADD dataUploaded :bytes SET updatedAt = :now',
      ExpressionAttributeValues: {
        ':bytes': additionalBytes,
        ':now': Date.now(),
      },
      ReturnValues: 'ALL_NEW',
    }));

    return result.Attributes as User;
  } catch (error) {
    console.error('Error updating user data usage:', error);
    throw new Error('Failed to update user data usage');
  }
}

/**
 * Validate if user can upload additional data
 */
export async function validateUserUpload(email: string, uploadSizeBytes: number): Promise<UserValidationResult> {
  const user = await getUserByEmail(email);
  if (!user) {
    throw new Error('User not found');
  }

  const currentLimit = user.status === UserStatus.PENDING 
    ? DATA_LIMITS.PENDING_USER_LIMIT 
    : DATA_LIMITS.ACTIVE_USER_LIMIT;

  const totalAfterUpload = user.dataUploaded + uploadSizeBytes;
  const remainingBytes = Math.max(0, currentLimit - user.dataUploaded);

  if (totalAfterUpload > currentLimit) {
    return {
      canUpload: false,
      remainingBytes,
      upgradeRequired: user.status === UserStatus.PENDING,
      message: user.status === UserStatus.PENDING 
        ? 'Please complete your account to unlock 1TB of storage'
        : 'Upload would exceed your storage limit',
    };
  }

  return {
    canUpload: true,
    remainingBytes,
    upgradeRequired: false,
  };
}

/**
 * Complete user account registration
 */
export async function completeUserAccount(
  email: string,
  name?: string,
  password?: string
): Promise<User> {
  const normalizedEmail = email.toLowerCase();

  try {
    let updateExpression = 'SET #status = :status, updatedAt = :now, activatedAt = :now';
    const expressionAttributeNames: Record<string, string> = { '#status': 'status' };
    const expressionAttributeValues: Record<string, any> = {
      ':status': UserStatus.ACTIVE,
      ':now': Date.now(),
      ':pendingStatus': UserStatus.PENDING,
    };

    if (name) {
      updateExpression += ', #name = :name';
      expressionAttributeNames['#name'] = 'name';
      expressionAttributeValues[':name'] = name;
    }

    if (password) {
      updateExpression += ', password = :password';
      expressionAttributeValues[':password'] = createHash('sha256').update(password).digest('hex');
    }

    const result = await docClient.send(new UpdateCommand({
      TableName: ENV.USER_TABLE,
      Key: { email: normalizedEmail },
      UpdateExpression: updateExpression,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
      ConditionExpression: '#status = :pendingStatus',
      ReturnValues: 'ALL_NEW',
    }));

    return result.Attributes as User;
  } catch (error: any) {
    if (error.name === 'ConditionalCheckFailedException') {
      throw new Error('User account is already active or not found');
    }
    console.error('Error completing user account:', error);
    throw new Error('Failed to complete account registration');
  }
}

/**
 * Get users by status (for admin/analytics purposes)
 */
export async function getUsersByStatus(status: UserStatus, limit: number = 100): Promise<User[]> {
  try {
    const result = await docClient.send(new QueryCommand({
      TableName: ENV.USER_TABLE,
      IndexName: 'StatusIndex',
      KeyConditionExpression: '#status = :status',
      ExpressionAttributeNames: { '#status': 'status' },
      ExpressionAttributeValues: { ':status': status },
      Limit: limit,
      ScanIndexForward: false, // Most recent first
    }));

    return result.Items as User[] || [];
  } catch (error) {
    console.error('Error getting users by status:', error);
    throw new Error('Failed to retrieve users');
  }
}

/**
 * Format bytes to human readable format
 */
export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
