import sgMail from '@sendgrid/mail';
import { SENDGRID_CONFIG } from './aws-clients';

export interface FileInfo {
  name: string;
  size: number;
  originalSize?: number;
}

export interface EmailShareRequest {
  transferId: string;
  recipientEmail: string;
  senderName?: string;
  shareLink: string;
  files: FileInfo[];
  totalSize: number;
  totalOriginalSize?: number;
  compressionRatio?: number;
  expiresAt: string;
  downloadLimit: number;
}

export interface EmailResponse {
  success: boolean;
  error?: string;
}

export interface WelcomeEmailRequest {
  email: string;
  verificationToken: string;
  baseUrl: string;
}

export interface UpgradeLimitEmailRequest {
  email: string;
  dataUsed: number;
  dataLimit: number;
  baseUrl: string;
}

// Initialize SendGrid
sgMail.setApiKey(SENDGRID_CONFIG.API_KEY);

/**
 * Send welcome email to new users
 */
export async function sendWelcomeEmail(request: WelcomeEmailRequest): Promise<EmailResponse> {
  try {
    const completionUrl = `${request.baseUrl}/complete-account?email=${encodeURIComponent(request.email)}&token=${request.verificationToken}`;

    const subject = 'Finish setting up your account and unlock 1TB of free storage';

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Complete Your FastTransfer Account</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
          .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 30px; text-align: center; }
          .header h1 { margin: 0; font-size: 28px; font-weight: 700; }
          .content { padding: 40px 30px; }
          .highlight { background: #f1f5f9; border-left: 4px solid #667eea; padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0; }
          .cta-button { display: inline-block; background: #667eea; color: white; text-decoration: none; padding: 16px 32px; border-radius: 8px; font-weight: 600; margin: 20px 0; transition: background 0.3s; }
          .cta-button:hover { background: #5a67d8; }
          .benefits { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .benefit-item { display: flex; align-items: center; margin: 10px 0; }
          .benefit-icon { width: 20px; height: 20px; background: #10b981; border-radius: 50%; margin-right: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; }
          .footer { background: #f8fafc; padding: 20px 30px; text-align: center; color: #64748b; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 Welcome to FastTransfer!</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">You're one step away from unlimited file sharing</p>
          </div>

          <div class="content">
            <p>Hi there!</p>

            <p>Thanks for starting your FastTransfer journey! You've successfully created your account, and now it's time to unlock the full power of our platform.</p>

            <div class="highlight">
              <strong>🎯 You're currently on our Starter plan with 100MB of storage.</strong><br>
              Complete your account setup to unlock <strong>1TB of free storage</strong> – that's 10,000x more space!
            </div>

            <div class="benefits">
              <h3 style="margin-top: 0; color: #1e293b;">What you'll get with a complete account:</h3>
              <div class="benefit-item">
                <div class="benefit-icon">✓</div>
                <span><strong>1TB of free storage</strong> – enough for thousands of files</span>
              </div>
              <div class="benefit-item">
                <div class="benefit-icon">✓</div>
                <span><strong>Advanced compression</strong> – save up to 90% on file sizes</span>
              </div>
              <div class="benefit-item">
                <div class="benefit-icon">✓</div>
                <span><strong>Password protection</strong> – keep your files secure</span>
              </div>
              <div class="benefit-item">
                <div class="benefit-icon">✓</div>
                <span><strong>Custom expiration</strong> – control how long files are available</span>
              </div>
              <div class="benefit-item">
                <div class="benefit-icon">✓</div>
                <span><strong>Transfer history</strong> – track all your uploads</span>
              </div>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${completionUrl}" class="cta-button">Complete My Account & Get 1TB Free</a>
            </div>

            <p style="color: #64748b; font-size: 14px;">
              This link will expire in 24 hours for security reasons. If you didn't create this account, you can safely ignore this email.
            </p>
          </div>

          <div class="footer">
            <p>FastTransfer - Send huge files, lightning fast</p>
            <p>If you have any questions, just reply to this email!</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const textContent = `
Welcome to FastTransfer!

Thanks for starting your FastTransfer journey! You've successfully created your account, and now it's time to unlock the full power of our platform.

You're currently on our Starter plan with 100MB of storage. Complete your account setup to unlock 1TB of free storage – that's 10,000x more space!

What you'll get with a complete account:
✓ 1TB of free storage – enough for thousands of files
✓ Advanced compression – save up to 90% on file sizes
✓ Password protection – keep your files secure
✓ Custom expiration – control how long files are available
✓ Transfer history – track all your uploads

Complete your account here: ${completionUrl}

This link will expire in 24 hours for security reasons. If you didn't create this account, you can safely ignore this email.

FastTransfer - Send huge files, lightning fast
If you have any questions, just reply to this email!
    `;

    const msg = {
      to: request.email,
      from: SENDGRID_CONFIG.FROM_EMAIL,
      subject,
      text: textContent,
      html: htmlContent,
    };

    await sgMail.send(msg);
    console.log(`Welcome email sent successfully to ${request.email}`);

    return { success: true };
  } catch (error: any) {
    console.error('Failed to send welcome email:', error);
    return {
      success: false,
      error: error.message || 'Failed to send welcome email'
    };
  }
}

/**
 * Send upgrade limit notification email
 */
export async function sendUpgradeLimitEmail(request: UpgradeLimitEmailRequest): Promise<EmailResponse> {
  try {
    const completionUrl = `${request.baseUrl}/complete-account?email=${encodeURIComponent(request.email)}`;
    const dataUsedMB = Math.round(request.dataUsed / (1024 * 1024));
    const dataLimitMB = Math.round(request.dataLimit / (1024 * 1024));

    const subject = 'Upgrade your account to continue uploading - 1TB free storage awaits!';

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Upgrade Your FastTransfer Account</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
          .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
          .header { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 40px 30px; text-align: center; }
          .header h1 { margin: 0; font-size: 28px; font-weight: 700; }
          .content { padding: 40px 30px; }
          .usage-bar { background: #f1f5f9; border-radius: 8px; overflow: hidden; margin: 20px 0; }
          .usage-fill { background: linear-gradient(90deg, #f59e0b, #d97706); height: 12px; width: 100%; }
          .cta-button { display: inline-block; background: #f59e0b; color: white; text-decoration: none; padding: 16px 32px; border-radius: 8px; font-weight: 600; margin: 20px 0; transition: background 0.3s; }
          .cta-button:hover { background: #d97706; }
          .highlight { background: #fef3c7; border-left: 4px solid #f59e0b; padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0; }
          .footer { background: #f8fafc; padding: 20px 30px; text-align: center; color: #64748b; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>⚡ Storage Limit Reached</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Upgrade now to continue uploading</p>
          </div>

          <div class="content">
            <p>Hi there!</p>

            <p>You've reached your ${dataLimitMB}MB storage limit on FastTransfer. You've been making great use of our service!</p>

            <div class="usage-bar">
              <div class="usage-fill"></div>
            </div>
            <p style="text-align: center; color: #64748b; font-size: 14px; margin-top: 8px;">
              ${dataUsedMB}MB of ${dataLimitMB}MB used (100%)
            </p>

            <div class="highlight">
              <strong>🎯 Ready to unlock 1TB of free storage?</strong><br>
              Complete your account setup in just 30 seconds and get <strong>10,000x more space</strong> – completely free!
            </div>

            <h3>What you'll get instantly:</h3>
            <ul style="padding-left: 20px;">
              <li><strong>1TB of free storage</strong> – upload thousands of files</li>
              <li><strong>Advanced compression</strong> – save up to 90% on file sizes</li>
              <li><strong>Password protection</strong> – secure your sensitive files</li>
              <li><strong>Custom expiration dates</strong> – control file availability</li>
              <li><strong>Transfer history</strong> – track all your uploads</li>
            </ul>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${completionUrl}" class="cta-button">Upgrade to 1TB Free Storage</a>
            </div>

            <p style="color: #64748b; font-size: 14px;">
              This upgrade is completely free and takes less than 30 seconds. No credit card required!
            </p>
          </div>

          <div class="footer">
            <p>FastTransfer - Send huge files, lightning fast</p>
            <p>Questions? Just reply to this email!</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const textContent = `
Storage Limit Reached - Upgrade to Continue

Hi there!

You've reached your ${dataLimitMB}MB storage limit on FastTransfer. You've been making great use of our service!

Ready to unlock 1TB of free storage? Complete your account setup in just 30 seconds and get 10,000x more space – completely free!

What you'll get instantly:
• 1TB of free storage – upload thousands of files
• Advanced compression – save up to 90% on file sizes
• Password protection – secure your sensitive files
• Custom expiration dates – control file availability
• Transfer history – track all your uploads

Upgrade here: ${completionUrl}

This upgrade is completely free and takes less than 30 seconds. No credit card required!

FastTransfer - Send huge files, lightning fast
Questions? Just reply to this email!
    `;

    const msg = {
      to: request.email,
      from: SENDGRID_CONFIG.FROM_EMAIL,
      subject,
      text: textContent,
      html: htmlContent,
    };

    await sgMail.send(msg);
    console.log(`Upgrade limit email sent successfully to ${request.email}`);

    return { success: true };
  } catch (error: any) {
    console.error('Failed to send upgrade limit email:', error);
    return {
      success: false,
      error: error.message || 'Failed to send upgrade limit email'
    };
  }
}

/**
 * Generate HTML email template for sharing download links
 */
function generateEmailTemplate(data: EmailShareRequest): string {
  const { files, totalSize, totalOriginalSize, compressionRatio, shareLink, expiresAt, downloadLimit, senderName } = data;
  
  const formatFileSize = (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  const senderText = senderName ? `${senderName} has` : 'Someone has';
  const fileCount = files.length;
  const fileText = fileCount === 1 ? 'file' : 'files';
  const expirationText = expiresAt ? `This link expires on ${formatDate(expiresAt)}.` : '';
  const downloadLimitText = downloadLimit ? `These ${fileText} can be downloaded ${downloadLimit} time${downloadLimit > 1 ? 's' : ''}.` : '';

  // Generate file list HTML
  const fileListHtml = files.map(file => `
    <div class="file-item">
      <div class="file-name">📁 ${file.name}</div>
      <div class="file-size">Size: ${formatFileSize(file.size)}</div>
    </div>
  `).join('');

  // Calculate compression info
  const compressionText = compressionRatio ?
    `<div class="compression-info">
      <strong>⚡ Compression:</strong> ${compressionRatio.toFixed(1)}% smaller than original
      ${totalOriginalSize ? `(${formatFileSize(totalOriginalSize)} → ${formatFileSize(totalSize)})` : ''}
    </div>` : '';

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Shared with You - FastTransfer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 32px;
        }
        .logo {
            display: inline-block;
            background: linear-gradient(135deg, #8b5cf6, #ff69b4);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .title {
            font-size: 28px;
            font-weight: bold;
            color: #1f2937;
            margin: 0;
        }
        .subtitle {
            font-size: 16px;
            color: #6b7280;
            margin: 8px 0 0 0;
        }
        .file-info {
            background: #f3f4f6;
            border-radius: 8px;
            padding: 20px;
            margin: 24px 0;
        }
        .file-item {
            background: white;
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
            border-left: 4px solid #8b5cf6;
        }
        .file-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }
        .file-size {
            font-size: 14px;
            color: #6b7280;
        }
        .compression-info {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 6px;
            padding: 12px;
            margin: 16px 0;
            font-size: 14px;
            color: #047857;
        }
        .total-info {
            background: #eff6ff;
            border-radius: 6px;
            padding: 12px;
            margin: 16px 0;
            font-size: 14px;
            color: #1e40af;
            font-weight: 600;
        }
        .download-button {
            display: inline-block;
            background: linear-gradient(135deg, #4f46e5, #8b5cf6);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 24px 0;
            transition: transform 0.2s;
        }
        .download-button:hover {
            transform: translateY(-2px);
        }
        .info-text {
            font-size: 14px;
            color: #6b7280;
            margin: 16px 0;
        }
        .footer {
            text-align: center;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
            font-size: 12px;
            color: #9ca3af;
        }
        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 12px;
            margin: 16px 0;
            font-size: 14px;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">⚡ FastTransfer</div>
            <h1 class="title">${fileCount > 1 ? 'Files' : 'File'} Shared with You!</h1>
            <p class="subtitle">${senderText} shared ${fileCount} ${fileText} with you</p>
        </div>

        <div class="total-info">
            📦 Total: ${fileCount} ${fileText} (${formatFileSize(totalSize)})
        </div>

        ${compressionText}

        <div class="file-info">
            ${fileListHtml}
        </div>

        <div style="text-align: center;">
            <a href="${shareLink}" class="download-button">
                ⬇️ Download ${fileCount > 1 ? 'All Files' : 'File'}
            </a>
        </div>

        ${expirationText || downloadLimitText ? `
        <div class="warning">
            <strong>⚠️ Important:</strong><br>
            ${expirationText} ${downloadLimitText}
        </div>
        ` : ''}

        <div class="info-text">
            <p>This file was compressed using our advanced ZMT technology for faster transfers. Click the download button above to get your file.</p>
            <p>If you have any issues downloading the file, please contact the person who sent it to you.</p>
        </div>

        <div class="footer">
            <p>This email was sent by FastTransfer - Ultra-fast file compression and transfer platform</p>
            <p>If you didn't expect this email, you can safely ignore it.</p>
        </div>
    </div>
</body>
</html>
  `.trim();
}

/**
 * Generate plain text email template for sharing download links
 */
function generateTextTemplate(data: EmailShareRequest): string {
  const { files, totalSize, totalOriginalSize, compressionRatio, shareLink, expiresAt, downloadLimit, senderName } = data;
  
  const formatFileSize = (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  const senderText = senderName ? `${senderName} has` : 'Someone has';
  const fileCount = files.length;
  const fileText = fileCount === 1 ? 'file' : 'files';
  const expirationText = expiresAt ? `This link expires on ${formatDate(expiresAt)}.` : '';
  const downloadLimitText = downloadLimit ? `These ${fileText} can be downloaded ${downloadLimit} time${downloadLimit > 1 ? 's' : ''}.` : '';

  // Generate file list text
  const fileListText = files.map(file =>
    `  - ${file.name} (${formatFileSize(file.size)})`
  ).join('\n');

  const compressionText = compressionRatio ?
    `\nCompression: ${compressionRatio.toFixed(1)}% smaller than original${totalOriginalSize ? ` (${formatFileSize(totalOriginalSize)} → ${formatFileSize(totalSize)})` : ''}` : '';

  return `
FastTransfer - ${fileCount > 1 ? 'Files' : 'File'} Shared with You!

${senderText} shared ${fileCount} ${fileText} with you:

Total: ${fileCount} ${fileText} (${formatFileSize(totalSize)})${compressionText}

Files:
${fileListText}

Download Link: ${shareLink}

${expirationText}
${downloadLimitText}

This file was compressed using our advanced ZMT technology for faster transfers. Click the download link above to get your file.

If you have any issues downloading the file, please contact the person who sent it to you.

---
This email was sent by FastTransfer - Ultra-fast file compression and transfer platform
If you didn't expect this email, you can safely ignore it.
  `.trim();
}

/**
 * Send email with download link using SendGrid
 */
export async function sendShareEmail(request: EmailShareRequest): Promise<EmailResponse> {
  try {
    const { recipientEmail, files, senderName } = request;
    
    // Validate email address
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
      return {
        success: false,
        error: 'Invalid email address'
      };
    }

    const fileCount = files.length;
    const fileText = fileCount === 1 ? 'file' : 'files';
    const fileNames = files.length === 1 ? files[0].name : `${files.length} files`;

    const subject = senderName
      ? `${senderName} shared ${fileNames} with you via FastTransfer`
      : `${fileNames} shared with you via FastTransfer`;

    const htmlBody = generateEmailTemplate(request);
    const textBody = generateTextTemplate(request);

    const msg = {
      to: recipientEmail,
      from: SENDGRID_CONFIG.FROM_EMAIL,
      subject: subject,
      text: textBody,
      html: htmlBody,
    };

    await sgMail.send(msg);

    return {
      success: true
    };

  } catch (error) {
    console.error('Failed to send email via SendGrid:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}
