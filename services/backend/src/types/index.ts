export interface User {
  email: string; // Primary key
  status: UserStatus;
  dataUploaded: number; // in bytes
  createdAt: number;
  updatedAt: number;
  name?: string;
  password?: string; // Only set when user completes registration
  activatedAt?: number; // When user completed full registration
}

export enum UserStatus {
  PENDING = 'pending',
  ACTIVE = 'active'
}

export interface Transfer {
  transferId: string;
  originalFiles: FileInfo[];
  compressedSize?: number;
  originalSize: number;
  compressionRatio?: number;
  status: TransferStatus;
  createdAt: number;
  expiresAt: number;
  downloadLimit?: number;
  downloadCount: number;
  password?: string;
  email?: string;
  userId?: string; // Link to user email
}

export interface FileInfo {
  fileName: string;
  fileSize: number;
  contentType: string;
  s3Key: string;
}

export interface Job {
  jobId: string;
  transferId: string;
  type: JobType;
  status: JobStatus;
  createdAt: number;
  updatedAt: number;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

export enum TransferStatus {
  UPLOADING = 'UPLOADING',
  UPLOADED = 'UPLOADED',
  COMPRESSING = 'COMPRESSING',
  COMPRESSED = 'COMPRESSED',
  READY = 'READY',
  DECOMPRESSING = 'DECOMPRESSING',
  ERROR = 'ERROR',
  EXPIRED = 'EXPIRED'
}

export enum JobType {
  COMPRESSION = 'COMPRESSION',
  DECOMPRESSION = 'DECOMPRESSION'
}

export enum JobStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export interface UploadRequest {
  files: {
    fileName: string;
    fileSize: number;
    contentType: string;
  }[];
  expirationHours?: number;
  downloadLimit?: number;
  password?: string;
  email?: string;
}

export interface UploadResponse {
  transferId: string;
  uploadUrls: {
    fileName: string;
    uploadUrl: string;
    s3Key: string;
  }[];
  expiresAt: number;
}

export interface LinkRequest {
  transferId: string;
  expirationHours?: number;
  downloadLimit?: number;
  password?: string;
}

export interface LinkResponse {
  transferId: string;
  shareUrl: string;
  expiresAt: number;
  downloadLimit?: number;
}

export interface DownloadRequest {
  transferId: string;
  password?: string;
}

export interface DownloadResponse {
  transferId: string;
  files: {
    fileName: string;
    downloadUrl: string;
    fileSize: number;
  }[];
  status: TransferStatus;
}

// User-related request/response types
export interface CreateUserRequest {
  email: string;
}

export interface CreateUserResponse {
  email: string;
  status: UserStatus;
  dataUploaded: number;
  message: string;
}

export interface CompleteAccountRequest {
  email: string;
  name?: string;
  password?: string;
  token: string; // Verification token from email
}

export interface CompleteAccountResponse {
  email: string;
  status: UserStatus;
  message: string;
}

export interface UserStatusResponse {
  email: string;
  status: UserStatus;
  dataUploaded: number;
  dataLimit: number;
  canUpload: boolean;
  upgradeRequired: boolean;
}
