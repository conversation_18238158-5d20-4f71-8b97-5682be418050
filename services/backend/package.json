{"name": "fast-transfer-backend", "version": "1.0.0", "description": "FastTransfer backend Lambda functions", "main": "dist/index.js", "scripts": {"build": "tsc", "watch": "tsc -w", "dev": "ts-node src/server.ts", "dev:watch": "nodemon --exec ts-node src/server.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "package": "npm run build && npm run zip", "zip": "cd dist && zip -r ../lambda-functions.zip .", "deploy": "aws lambda update-function-code --function-name FastTransfer-Upload --zip-file fileb://lambda-functions.zip"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.400.0", "@aws-sdk/client-s3": "^3.842.0", "@aws-sdk/client-ses": "^3.840.0", "@aws-sdk/client-sqs": "^3.400.0", "@aws-sdk/lib-dynamodb": "^3.400.0", "@aws-sdk/lib-storage": "^3.842.0", "@aws-sdk/s3-request-presigner": "^3.400.0", "@sendgrid/mail": "^8.1.3", "cors": "^2.8.5", "crypto": "^1.0.1", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "stream-pipeline": "^0.0.4", "tmp": "^0.2.3", "uuid": "^9.0.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.119", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.5", "@types/multer": "^1.4.7", "@types/multer-s3": "^3.0.3", "@types/node": "^20.6.0", "@types/tmp": "^0.2.6", "@types/uuid": "^9.0.4", "concurrently": "^9.2.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/tests/**"]}}