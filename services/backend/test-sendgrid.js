#!/usr/bin/env node

/**
 * Test script for SendGrid email functionality
 * This script tests the new SendGrid integration with multiple file details
 */

const { sendShareEmail } = require('./dist/lib/email-service');

// Test data mimicking a real transfer with multiple files
const testEmailData = {
  transferId: 'test-transfer-123',
  recipientEmail: '<EMAIL>', // Change this to your email for testing
  senderName: 'Test User',
  shareLink: 'http://localhost:5174/share/test-transfer-123',
  files: [
    {
      name: 'document.pdf',
      size: 2048576, // 2MB
      originalSize: 3145728 // 3MB
    },
    {
      name: 'image.jpg',
      size: 1048576, // 1MB
      originalSize: 1572864 // 1.5MB
    },
    {
      name: 'data.csv',
      size: 512000, // 500KB
      originalSize: 768000 // 750KB
    }
  ],
  totalSize: 3608576, // ~3.4MB total compressed
  totalOriginalSize: 5485760, // ~5.2MB total original
  compressionRatio: 34.2, // 34.2% compression
  expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
  downloadLimit: 5
};

async function testSendGridEmail() {
  console.log('🧪 Testing SendGrid Email Integration...\n');
  
  // Check environment variables
  if (!process.env.SENDGRID_API_KEY) {
    console.error('❌ SENDGRID_API_KEY environment variable is not set');
    console.log('Please set your SendGrid API key:');
    console.log('export SENDGRID_API_KEY="your-sendgrid-api-key-here"');
    process.exit(1);
  }
  
  if (!process.env.FROM_EMAIL) {
    console.log('⚠️  FROM_EMAIL not set, using default: <EMAIL>');
  }
  
  console.log('📧 Test Email Data:');
  console.log(`   Recipient: ${testEmailData.recipientEmail}`);
  console.log(`   Sender: ${testEmailData.senderName}`);
  console.log(`   Files: ${testEmailData.files.length}`);
  console.log(`   Total Size: ${(testEmailData.totalSize / 1024 / 1024).toFixed(2)} MB`);
  console.log(`   Compression: ${testEmailData.compressionRatio}%`);
  console.log(`   Share Link: ${testEmailData.shareLink}`);
  console.log('');
  
  try {
    console.log('📤 Sending test email...');
    const result = await sendShareEmail(testEmailData);
    
    if (result.success) {
      console.log('✅ Email sent successfully!');
      console.log('📬 Check your inbox for the test email');
      console.log('');
      console.log('The email should include:');
      console.log('  • Download link');
      console.log('  • List of all 3 files with sizes');
      console.log('  • Compression information');
      console.log('  • Expiration and download limit details');
    } else {
      console.error('❌ Failed to send email:', result.error);
    }
    
  } catch (error) {
    console.error('💥 Error during email test:', error.message);
    
    if (error.message.includes('API key')) {
      console.log('\n🔑 API Key Issues:');
      console.log('  • Make sure your SendGrid API key is valid');
      console.log('  • Check that the API key has "Mail Send" permissions');
      console.log('  • Verify the API key is not expired');
    }
    
    if (error.message.includes('from')) {
      console.log('\n📧 From Email Issues:');
      console.log('  • Make sure the FROM_EMAIL is verified in SendGrid');
      console.log('  • Check that the domain is authenticated');
    }
  }
}

// Run the test
testSendGridEmail().catch(console.error);
