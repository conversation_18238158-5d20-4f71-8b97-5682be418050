# FastTransfer Cache Configuration Guide

This document outlines the comprehensive caching strategy implemented across the FastTransfer platform for optimal performance and user experience.

## Overview

The FastTransfer platform implements a multi-tier caching strategy that includes:
- **CloudFront CDN** - Global edge caching with custom behaviors
- **Backend Server** - Application-level cache headers
- **File Type Optimization** - Specialized caching for different content types

## CloudFront CDN Cache Behaviors

### 1. API Routes (`/api/*`)
- **Cache Policy**: Disabled (no caching)
- **Purpose**: Dynamic content that must always be fresh
- **Headers**: Forward all headers for proper API functionality
- **Compression**: Enabled for response optimization

### 2. Versioned Assets (`/assets/*`)
- **Cache Duration**: 365 days (immutable)
- **Purpose**: Static assets with hash-based versioning
- **Compression**: Gzip + Brotli enabled
- **Headers**: None forwarded (aggressive caching)

### 3. ZMT Compressed Files (`*.zmt`)
- **Cache Duration**: 7 days (default), up to 30 days (max)
- **Purpose**: Medium-term caching for compressed files
- **Compression**: Disabled (already compressed)
- **Range Requests**: Supported for partial downloads
- **Headers**: `Range`, `If-Range` forwarded

### 4. Archive Files (`*.zip`)
- **Cache Duration**: 7 days (default), up to 30 days (max)
- **Purpose**: Medium-term caching for archive files
- **Compression**: Disabled (already compressed)
- **Range Requests**: Supported for large file downloads
- **File Types**: ZIP, RAR, 7Z, TAR, GZ, BZ2

### 5. HTML Files (`*.html`)
- **Cache Duration**: 5 minutes (default), up to 1 hour (max)
- **Purpose**: Short-term caching with frequent revalidation
- **Compression**: Gzip + Brotli enabled
- **Headers**: `Cache-Control` forwarded for revalidation

### 6. Document Files (`*.pdf`)
- **Cache Duration**: 1 day (default), up to 7 days (max)
- **Purpose**: Medium-term caching for documents
- **Compression**: Enabled for text-based documents
- **Range Requests**: Supported for large documents
- **File Types**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX

### 7. Media Files (`*.mp4`)
- **Cache Duration**: 30 days (default), up to 365 days (max)
- **Purpose**: Long-term caching for media content
- **Compression**: Disabled (media files are pre-compressed)
- **Range Requests**: Full support for streaming
- **Headers**: `Range`, `If-Range`, `Accept-Ranges` forwarded
- **File Types**: MP4, AVI, MOV, WMV, FLV, WEBM, MKV, MP3, WAV, FLAC, AAC

## Backend Server Cache Headers

### File Type Specific Caching

#### ZMT Files (`.zmt`)
```http
Cache-Control: public, max-age=604800, stale-while-revalidate=86400
Content-Encoding: identity
Accept-Ranges: bytes
Content-Type: application/octet-stream
```
- **Duration**: 7 days with 1-day stale-while-revalidate
- **Purpose**: Optimized for ZMT compressed file delivery

#### Archive Files (`.zip`, `.rar`, `.7z`, `.tar`, `.gz`, `.bz2`)
```http
Cache-Control: public, max-age=604800, stale-while-revalidate=86400
Content-Encoding: identity
Accept-Ranges: bytes
Content-Type: application/octet-stream
```
- **Duration**: 7 days with background revalidation
- **Purpose**: Efficient delivery of compressed archives

#### Document Files (`.pdf`, `.doc`, `.docx`, `.xls`, `.xlsx`, `.ppt`, `.pptx`)
```http
Cache-Control: public, max-age=86400, stale-while-revalidate=3600
Accept-Ranges: bytes
Vary: Accept-Encoding
```
- **Duration**: 1 day with 1-hour background revalidation
- **Purpose**: Balance between freshness and performance

#### Media Files (`.mp4`, `.avi`, `.mov`, `.mp3`, `.wav`, etc.)
```http
Cache-Control: public, max-age=2592000, stale-while-revalidate=86400
Content-Encoding: identity
Accept-Ranges: bytes
```
- **Duration**: 30 days with streaming support
- **Purpose**: Long-term caching for media content

#### HTML Files (`.html`, `.htm`)
```http
Cache-Control: public, max-age=300, must-revalidate, stale-while-revalidate=60
Vary: Accept-Encoding
```
- **Duration**: 5 minutes with mandatory revalidation
- **Purpose**: Fresh content delivery with minimal caching

#### Text Files (`.txt`, `.log`, `.csv`, `.json`, `.xml`)
```http
Cache-Control: public, max-age=3600, stale-while-revalidate=300
Vary: Accept-Encoding
```
- **Duration**: 1 hour with compression optimization
- **Purpose**: Moderate caching for text-based content

#### Images (`.png`, `.jpg`, `.jpeg`, `.gif`, `.svg`, `.ico`, `.webp`, `.avif`)
```http
# Versioned images (with hash)
Cache-Control: public, max-age=31536000, immutable

# Non-versioned images
Cache-Control: public, max-age=86400, stale-while-revalidate=3600
Vary: Accept-Encoding
```
- **Duration**: 1 year for versioned, 1 day for non-versioned
- **Purpose**: Optimal image delivery with format support

#### Fonts (`.woff`, `.woff2`, `.ttf`, `.eot`)
```http
Cache-Control: public, max-age=2592000, immutable
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET
```
- **Duration**: 30 days with CORS support
- **Purpose**: Long-term font caching with cross-origin access

## Security Headers

All cached content includes security headers:
```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

## Cache Invalidation Strategy

### Automatic Invalidation
- **Versioned Assets**: No invalidation needed (immutable)
- **HTML Files**: Short TTL ensures fresh content
- **API Responses**: No caching prevents stale data

### Manual Invalidation
- CloudFront invalidation for emergency updates
- Deployment scripts handle cache busting
- Version-based asset naming prevents cache issues

## Performance Benefits

### Bandwidth Reduction
- **Compression**: Gzip/Brotli for compressible content
- **Range Requests**: Partial downloads for large files
- **Edge Caching**: Reduced origin server load

### Latency Optimization
- **Global CDN**: Content served from nearest edge location
- **Stale-While-Revalidate**: Instant responses with background updates
- **Immutable Assets**: Permanent browser caching

### User Experience
- **Progressive Loading**: Range requests enable streaming
- **Offline Resilience**: Cached content available during network issues
- **Fast Subsequent Visits**: Aggressive caching for static assets

## Monitoring and Analytics

### Cache Hit Rates
- CloudFront metrics track cache performance
- Backend analytics monitor download patterns
- Performance metrics identify optimization opportunities

### File Type Analytics
- Download tracking by file extension
- Cache effectiveness per content type
- User behavior analysis for cache tuning

## Best Practices

### Content Delivery
1. Use versioned filenames for long-term caching
2. Implement proper MIME types for all file formats
3. Enable compression for text-based content
4. Support range requests for large files

### Cache Management
1. Set appropriate TTL values based on content type
2. Use stale-while-revalidate for better UX
3. Implement proper cache invalidation strategies
4. Monitor cache hit rates and adjust policies

### Security Considerations
1. Include security headers with all responses
2. Validate file types before setting cache headers
3. Implement proper CORS policies for fonts
4. Use secure download mechanisms for sensitive files

This comprehensive caching strategy ensures optimal performance while maintaining content freshness and security across the FastTransfer platform.
